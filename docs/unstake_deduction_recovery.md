# 解压额度恢复功能实现文档

## 功能概述

实现了一个功能：如果用户今天进行了解压操作（扣减了质押额度），当用户参与活动质押时，能够把解压时扣减的质押额度加回来，但不能超过当天扣减的数量。

## 实现方案

### 1. 数据库变更

在 `stake_user` 表中新增字段：
```sql
ALTER TABLE dop.stake_user 
ADD COLUMN today_unstake_deduction DECIMAL(20,8) DEFAULT 0.00000000 COMMENT '今日解压扣减的额度';
```

### 2. 代码变更

#### 2.1 模型层变更
- **StakeUser.java**: 新增 `todayUnstakeDeduction` 字段

#### 2.2 数据访问层变更
- **StakeUserMapper.java**: 新增三个方法
  - `updateUserLimitAmountWithDeduction`: 解压时扣减额度并记录
  - `recoverUserLimitAmount`: 恢复额度
  - `resetTodayUnstakeDeduction`: 重置今日扣减额度

#### 2.3 业务逻辑层变更
- **StakeUserServiceImpl.java**: 修改解压逻辑，使用新的扣减方法
- **ActivityServiceImpl.java**: 在活动质押时添加额度恢复逻辑

#### 2.4 每日结算任务
- **Job7.java**: 在每日结算任务中添加重置今日扣减额度的逻辑

## 业务流程

### 解压流程
1. 用户执行解压操作
2. 系统计算扣减额度（解压数量 × 4）
3. 扣减 `stakeLimit` 和 `burnLimit`
4. 将扣减数量累加到 `todayUnstakeDeduction`

### 活动质押流程
1. 用户执行活动质押操作
2. 系统检查用户的 `todayUnstakeDeduction`
3. 如果有扣减额度，则按比例恢复：
   - 计算应恢复额度 = 活动质押数量 × 4（与解压扣减倍数一致）
   - 实际恢复额度 = min(应恢复额度, 今日扣减额度)
4. 恢复相应数量到 `stakeLimit` 和 `burnLimit`
5. 将 `todayUnstakeDeduction` 减去已恢复的数量
6. 继续执行正常的活动质押逻辑

### 每日重置流程
1. 在每日结算任务(Job7)中执行重置逻辑
2. 将所有用户的 `todayUnstakeDeduction` 重置为0

## 使用示例

### 场景1：当天解压后参与活动质押
```
用户张三初始状态：
- stakeLimit: 1000
- burnLimit: 1000
- todayUnstakeDeduction: 0

1. 上午10点解压100：
   - stakeLimit: 1000 → 600 (扣减400)
   - burnLimit: 1000 → 600 (扣减400)
   - todayUnstakeDeduction: 0 → 400

2. 下午2点参与活动质押100：
   - 系统检测到todayUnstakeDeduction = 400
   - 计算应恢复额度 = 100 × 4 = 400
   - 实际恢复额度 = min(400, 400) = 400
   - stakeLimit: 600 → 1000 (恢复400)
   - burnLimit: 600 → 1000 (恢复400)
   - todayUnstakeDeduction: 400 → 0
   - 继续执行活动质押
```

### 场景2：当天解压后参与小额活动质押（部分恢复）
```
用户李四初始状态：
- stakeLimit: 1000
- burnLimit: 1000
- todayUnstakeDeduction: 0

1. 上午10点解压125：
   - stakeLimit: 1000 → 500 (扣减500)
   - burnLimit: 1000 → 500 (扣减500)
   - todayUnstakeDeduction: 0 → 500

2. 下午2点参与活动质押50：
   - 系统检测到todayUnstakeDeduction = 500
   - 计算应恢复额度 = 50 × 4 = 200
   - 实际恢复额度 = min(200, 500) = 200
   - stakeLimit: 500 → 700 (恢复200)
   - burnLimit: 500 → 700 (恢复200)
   - todayUnstakeDeduction: 500 → 300
   - 继续执行活动质押
```

## 关键特性

1. **当日限制**: 只能恢复当天解压扣减的额度
2. **按比例恢复**: 恢复额度 = min(活动质押数量 × 4, 今日扣减额度)
3. **不超量恢复**: 恢复的额度不会超过扣减的数量
4. **支持多次**: 支持多次部分恢复，每次按当前质押数量计算
5. **每日重置**: 每天重置，避免跨天累积
6. **事务安全**: 所有操作都在事务中执行，保证数据一致性

## 测试

运行测试类 `UnstakeDeductionRecoveryTest` 来验证功能：
```bash
mvn test -Dtest=UnstakeDeductionRecoveryTest
```

## 部署说明

1. 执行数据库脚本：新字段SQL已追加到 `src/test/ddl.sql` 文件底部
2. 部署应用代码
3. 验证每日结算任务(Job7)正常运行

## 注意事项

1. 数据库字段使用 `DECIMAL(20,8)` 类型，与其他金额字段保持一致
2. 重置逻辑集成在每日结算任务中，与其他每日操作一起执行
3. 恢复操作使用乐观锁，确保并发安全
4. 所有金额计算使用 `BigDecimal`，避免精度问题
