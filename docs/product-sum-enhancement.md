# Product/Sum 接口增强 - 伞下网体产币量统计

## 功能概述

在现有的 `product/sum` 接口基础上，新增了4个锁仓相关的统计字段，用于统计伞下用户在指定时间段内的各种收益数据。

## 新增字段

### 1. stakeStaticAmount - 锁仓静态收益
统计伞下用户的静态收益总和，包括：
- 类型 2：活期收益 (CurrentProfit)
- 类型 3：定期收益 (RegularProfit)

### 2. stakeDynamicAmount - 锁仓动态收益
统计伞下用户的动态收益总和，包括：
- 类型 4：返佣奖励 (InviteProfit)

### 3. stakeDirectAmount - 锁仓直推收益
统计伞下用户的直推收益总和，包括：
- 类型 10：定期直推奖励 (BuyRegularReward)

### 4. stakeReleaseAmount - 灵活质押每天释放
统计伞下用户灵活质押每天释放的总和：
- 类型 41：分期释放 (StakeRelease)

## 接口使用

### 请求示例
```http
GET /admin/product/sum?id=123456&beginTime=2024-01-01&endTime=2024-12-31
```

### 响应示例
```json
{
  "code": 200,
  "data": {
    "id": "123456",
    "productCurrentAmount": 1000.00,
    "productRegularAmount": 5000.00,
    // ... 其他现有字段
    "stakeStaticAmount": 71907.17,
    "stakeDynamicAmount": 4397.12,
    "stakeDirectAmount": 0.00,
    "stakeReleaseAmount": 1.52
  }
}
```

## 收益类型说明

### 收益分类标准
- **静态收益**：类型 2（活期收益）、3（定期收益）
- **动态收益**：类型 4（返佣奖励）
- **直推收益**：类型 10（定期直推奖励）
- **每天释放**：类型 41（分期释放）

## 技术实现

### 数据来源
所有新增统计数据都来自 `user_log` 表，通过 `type` 字段区分不同的收益类型。

### 查询逻辑
1. 获取指定用户的所有伞下用户ID
2. 根据时间范围和用户ID列表查询 `user_log` 表
3. 按收益类型分组统计金额（类型：2,3,4,10,41）
4. 将统计结果合并到现有的 `UserModel` 中返回

### 修改的文件
- `src/main/java/com/aic/app/model/UserModel.java` - 新增4个统计字段
- `src/main/java/com/aic/app/mapper/UserProductMapper.java` - 新增统计查询方法
- `src/main/java/com/aic/app/job/Job3.java` - 修改计算逻辑，集成锁仓统计

## 注意事项

1. 所有金额字段默认初始化为 `BigDecimal.ZERO`
2. 查询使用 `dop.user_log` 表，遵循数据库命名规范
3. 时间范围查询支持可选参数，不传时间则统计全部数据
4. 支持通过用户ID或邀请码查询
5. 收益类型严格按照业务需求分类：静态(2,3)、动态(4)、直推(10)、释放(41)
