-- Active: 1740714108117@@127.0.0.1@3306@dop
# create database dop charset = utf8mb4;

drop table if exists dop.user;
create table dop.user
(
    id      varchar(32)              not null,
    code varchar(32) null comment '我的邀请码',
    ref_code varchar(32) null comment '上级邀请码',
    pid varchar(32)  null comment '邀请人ID',
    level int(11) not null default 1 comment '等级',
    limit_amount decimal(25, 8) default 1000 not null comment '每日限额',
    current_amount decimal(25, 8) default 0 not null comment '活期限额',
    pending_amount decimal(25, 8) default 0 not null comment '待确认',
    total_amount decimal(25, 8) default 0 not null comment '总理财数量',
    sum_amount decimal(25, 8) default 0 not null comment '伞下理财数量',
    sum_current_amount decimal(25, 8) default 0 not null comment '伞下活期理财数量',
    sum_regular_amount decimal(25, 8) default 0 not null comment '伞下定期理财数量',
    balance decimal(25, 8) default 0 not null comment 'DOP余额',
    today_static decimal(25, 8) default 0 not null comment '今日静态收益',
    total_static decimal(25, 8) default 0 not null comment '累计静态收益',
    today_dynamic decimal(25, 8) default 0 not null comment '今日动态收益',
    total_dynamic decimal(25, 8) default 0 not null comment '累计动态收益',
    today_buy decimal(25, 8) default 0 not null comment '今日购买',
    total_buy decimal(25, 8) default 0 not null comment '累计购买',
    team_count int(11) not null default 1 comment '团队人数',
    can_receive decimal(25, 8) default 0 not null comment '可领取',
    lock_level tinyint(1) default 0 not null comment '是否锁定等级',
    openid          varchar(32) null comment '',
    sum_amount365 decimal(25, 8) default 0 not null comment '直推定期理财360数量',
    create_time datetime not null default now() comment '创建时间',
    constraint id
        primary key (id)
)
    comment '用户表';


drop table if exists dop.product;
create table dop.product
(
    id      int              not null auto_increment,
    type int(1) not null comment '类型',
    name varchar(128) not null comment '名称',
    pay_method json   not null comment '支付方式',
    rate decimal(25,6) not null comment '日收益率',
    day int(11) not null comment '天数',
    enable bigint(1) not null default 0 comment '是否有效',
    constraint id
        primary key (id)
)
    comment '理财产品表';

truncate table dop.product;
insert into dop.product(id, type, name, pay_method, rate, day, enable) values (1, 0, '活期', '{"DOP": {"max":"1"}}', 0.002, 1, 1);
insert into dop.product(id, type, name, pay_method, rate, day, enable) values (2, 1, '30天', '{"DOP": {"max":"1.2"}}', 0.004, 30, 1);
insert into dop.product(id, type, name, pay_method, rate, day, enable) values (3, 1, '180天币', '{"DOP": {"max":"2"},"DOP+FISH": {"max":"2"},"DOP+BIRD": {"max":"2"}}', 0.006, 180, 1);
insert into dop.product(id, type, name, pay_method, rate, day, enable) values (4, 1, '360天币', '{"DOP": {"max":"3"},"DOP+FISH": {"max":"3"},"DOP+BIRD": {"max":"3"}}', 0.008, 360, 1);
insert into dop.product(id, type, name, pay_method, rate, day, enable) values (5, 1, '360天金', '{"DOP": {"max":"3"},"DOP+FISH": {"max":"3"},"DOP+BIRD": {"max":"3"}}', 0.008, 360, 1);
insert into dop.product(id, type, name, pay_method, rate, day, enable) values (6, 1, '360天币', '{"DOP": {"max":"1"}}', 0.008, 360, 0);
insert into dop.product(id, type, name, pay_method, rate, day, enable) values (7, 1, '180天金', '{"DOP": {"max":"2"},"DOP+FISH": {"max":"2"},"DOP+BIRD": {"max":"2"}}', 0.006, 180, 1);



drop table if exists dop.user_product;
create table dop.user_product
(
    id      int              not null auto_increment,
    user_id varchar(32) not null comment '用户ID',
    type int(1) not null comment '类型 0-活期 1-定期',
    order_no varchar(32) not null comment '订单号',
    product_id int(1) not null comment '产品ID',
    power decimal(25,8) not null default 0 comment '理财算力',
    profit decimal(25,8) not null default 0 comment '利息',
    rate decimal(25,6) not null comment '日收益率',
    max_rate decimal(25,6) not null comment '最大收益率',
    pay_method varchar(32) not null comment '支付方式',
    day int(11) not null comment '天数',
    release_day int(11) not null comment '发放天数',
    create_time datetime not null comment '创建时间',
    expire_time datetime not null comment '到期时间',
    status bigint(1) not null default 0 comment '0-待确认 1-有效，2-已经期',
    price1 decimal(25,8) not null default 0 comment '币种1价格',
    price2 decimal(25,8) not null default 0 comment '币种2价格',
    amount1 decimal(25,8) not null default 0 comment '币种1数量',
    amount2 decimal(25,8) not null default 0 comment '币种2数量',
    usd_amount decimal(25, 8) default 0 null comment '本金',
    redeem_amount decimal(25, 8) default 0 null comment '赎回额度',
    constraint id
        primary key (id)
)
    comment '用户理财产品表';



drop table if exists dop.user_log;
create table dop.user_log
(
    id      int              not null auto_increment,
    user_id varchar(32) not null comment '用户ID',
    type int(2) not null comment '类型',
    product_amount decimal(25,8) not null comment '理财金额',
    amount decimal(25,8) not null comment '金额',
    last_amount decimal(25,8) not null default 0 comment '金额',
    remark varchar(128) null comment '备注',
    symbol varchar(32) DEFAULT 'DOP' null comment '币种',
    create_time datetime not null comment '创建时间',
    primary key (id)
)
    comment '用户流水';

drop table if exists dop.reward_log;
create table dop.reward_log
(
    id      int              not null auto_increment,
    user_id varchar(32) not null comment '用户ID',
    child_user_id varchar(32) not null comment '用户ID',
    level int(2) not null comment '等级',
    product_amount decimal(25,8) not null default 0 comment '理财数量',
    amount decimal(25,8) not null comment '返佣数量',
    create_time datetime not null comment '创建时间',
    primary key (id)
)
    comment '返佣奖励';

alter table dop.reward_log add column symbol varchar(32) DEFAULT 'DOP' null comment '币种';


truncate table dop.user;

# insert into dop.user(id, code, pid, level, limit_amount, current_amount, total_amount) VALUES
# (1,'AAAAAA',null, 3, 1000, 100, 100),
# (2,'BBBBBB',1, 2, 1000, 100, 100),
# (3,'CCCCCC',2, 1, 1000, 100, 100),
# (4,'CCCCCC',3, 0, -1, 500, 2750);


drop table if exists dop.asset;
create table dop.asset
(
    id         int         not null auto_increment,
    token_id   varchar(32) not null comment '资产ID',
    token_name varchar(32) not null comment '资产名称',
    primary key (id)
)
    comment '资产';

insert into dop.asset(id, token_id, token_name)
VALUES (1, 'DOP', 'DOP'),
       (2, 'FISH', 'FISH'),
       (3, 'FISH1', 'FISH1'),
       (4, 'BIRD', 'BIRD'),
       (5, 'BIRD1', 'BIRD1');


drop table if exists dop.user_asset;
create table dop.user_asset
(
    id          int                      not null auto_increment,
    user_id     varchar(32)              not null comment '用户ID',
    token_id    varchar(32)              not null comment '币种',
    balance     decimal(25, 8) default 0 not null comment '余额',
    version     int(11)        default 0 not null comment '版本',
    reason      varchar(128)   default '' null comment '备注',    
    create_time datetime                 not null default now() comment '创建时间',
    constraint id
        primary key (id)
)
    comment '手机用户资产';


drop table if exists dop.asset_transfer;
create table dop.asset_transfer
(
    id           int                      not null auto_increment,
    type         int(1)                   not null comment '划转类型 0-手机到平台 1-平台到手机',
    order_no       varchar(32)              null comment '订单号',
    user_id      varchar(32)              not null comment '用户ID',
    token_id     varchar(32)              not null comment '币种',
    quantity     decimal(25, 8) default 0 not null comment '数量',
    status       int(1)         default 0 not null comment '状态 0-待确认 1-成功',
    confirm_time datetime                 null comment '确认时间',
    create_time  datetime                 not null default now() comment '创建时间',
    constraint id
        primary key (id)
)
    comment '手机用户划转记录';



drop table if exists dop.burn_log;
create table dop.burn_log
(
    id      int              not null auto_increment,
    day date not null comment '日期',
    amount decimal(25,8) not null comment '金额',
    create_time datetime not null comment '创建时间',
    primary key (id)
)
    comment '销毁记录';

alter table dop.burn_log add constraint date unique (day);


drop table if exists dop.sys_config;
create table dop.sys_config
(
    id      int              not null auto_increment,
    reward_limit decimal(25,8) not null comment '金额',
    day int(11) default 0 not null comment 'N',
    primary key (id)
)
    comment '系统配置表';

insert into dop.sys_config(id, reward_limit) values (1, 5000000000);


# update dop.user_asset set token_id='FISH1' where token_id='FISH2';
# update dop.user_asset set token_id='BIRD1' where token_id='BIRD2';


drop table if exists dop.reward_day;
create table dop.reward_day
(
    id          int                      not null auto_increment,
    day     varchar(32)              not null comment '日期',
    total_static     decimal(25, 8) default 0 not null comment '总静态',
    total_dynamic     decimal(25, 8) default 0 not null comment '总动态',
    total_reward     decimal(25, 8) default 0 not null comment '总直推',
    total_amount     decimal(25, 8) default 0 not null comment '总发放金额',
    create_time datetime                 not null default now() comment '创建时间',
    constraint id
        primary key (id)
)
    comment '每日结算记录';


drop table if exists dop.user_watch;
create table dop.user_watch
(
    id      varchar(32)              not null,
    create_time datetime not null default now() comment '创建时间',
    constraint id
        primary key (id)
)
    comment '用户表';

alter table dop.reward_log add column product_id int null comment '用户下单ID';
alter table dop.reward_log add column pay_method varchar(32) null comment '支付方式';

# update reward_log a, user_product b set a.product_id=b.id, a.pay_method=b.pay_method where a.level=-1 and a.child_user_id=b.user_id and abs(a.create_time - b.create_time) <= 1;

insert into dop.asset(id, token_id, token_name)
VALUES (6, 'NAC', 'NAC'),
       (7, 'NAC-NA', 'NAC-NA'),
       (8, 'FOMO', 'FOMO'),
       (9, 'FOMOX', 'FOMOX');

# update dop.asset set token_id='NAC-NA', token_name='NAC-NA' where token_id='NAC1';
# update dop.asset set token_id='FOMOX', token_name='FOMOX' where token_id='FOMO1';
# 
# update dop.user_asset set token_id='NAC-NA' where token_id='NAC1';
# update dop.user_asset set token_id='FOMOX' where token_id='FOMO1';


INSERT INTO dop.user_asset (user_id, token_id, balance, version, reason, create_time)
SELECT u.id, 'NAC', 0.00000000, 0, null, CURRENT_TIMESTAMP
FROM dop.user u LEFT JOIN dop.user_asset ua ON u.id = ua.user_id and ua.token_id = 'NAC' WHERE ua.user_id IS NULL;

INSERT INTO dop.user_asset (user_id, token_id, balance, version, reason, create_time)
SELECT u.id, 'NAC-NA', 0.00000000, 0, null, CURRENT_TIMESTAMP
FROM dop.user u LEFT JOIN dop.user_asset ua ON u.id = ua.user_id and ua.token_id = 'NAC-NA' WHERE ua.user_id IS NULL;

INSERT INTO dop.user_asset (user_id, token_id, balance, version, reason, create_time)
SELECT u.id, 'FOMO', 0.00000000, 0, null, CURRENT_TIMESTAMP
FROM dop.user u LEFT JOIN dop.user_asset ua ON u.id = ua.user_id and ua.token_id = 'FOMO' WHERE ua.user_id IS NULL;

INSERT INTO dop.user_asset (user_id, token_id, balance, version, reason, create_time)
SELECT u.id, 'FOMOX', 0.00000000, 0, null, CURRENT_TIMESTAMP
FROM dop.user u LEFT JOIN dop.user_asset ua ON u.id = ua.user_id and ua.token_id = 'FOMOX' WHERE ua.user_id IS NULL;


update dop.product set pay_method='{"DOP": {"max":"2"},"DOP+FISH": {"max":"2"},"DOP+BIRD": {"max":"2"},"DOP+NAC": {"max":"2"},"DOP+FOMO": {"max":"2"}}' where id in (7);
update dop.product set pay_method='{"DOP": {"max":"3"},"DOP+FISH": {"max":"3"},"DOP+BIRD": {"max":"3"},"DOP+NAC": {"max":"3"},"DOP+FOMO": {"max":"3"}}' where id in (5);


alter table dop.reward_log add column price decimal(25,8) null comment '币种价格';


drop table if exists dop.user_relation;
create table dop.user_relation
(
    id   varchar(32) not null,
    pid  varchar(32) null,
    layer int not null default 0,
    path text,
    primary key (id)
);


alter table dop.user drop column ref_code;

# alter table dop.user_log add column product_id int(1) null comment '产品ID';

# 1230
# select count(*) from dop.user_product a where a.product_id=6
# 1230
# select count(*) from dop.user_product a, dop.user_log b where a.product_id=6 and b.type=1 and ABS(UNIX_TIMESTAMP(a.create_time) - UNIX_TIMESTAMP(b.create_time)) <= 1 and b.symbol='DOP' and a.user_id=b.user_id and a.amount1=b.amount;
# create table dop.user_log_del select b.* from dop.user_product a, dop.user_log b where a.product_id=6 and b.type=1 and ABS(UNIX_TIMESTAMP(a.create_time) - UNIX_TIMESTAMP(b.create_time)) <= 1 and b.symbol='DOP' and a.user_id=b.user_id and a.amount1=b.amount;
# delete from dop.user_log where id in (select b.id from dop.user_log_del b);



# select id,user_id,type,if(type in (2,3,4,5,6,10,12,13,15), abs(amount), -abs(amount)) from dop.user_log where user_id='0d52ceda380941679b9a779ea63f4c5b' and type in (1,2,3,4,6,10,11,12,13,15);

# 2025-02-02 15:26:44

# select sum(profit) from dop.user_product

alter table dop.user drop column set_level_time;
alter table dop.user add column set_level_time datetime null comment '设置等级时间';
alter table dop.user add column real_level int not null default 0 comment '真实等级';
# update dop.user set set_level_time=now() where lock_level=1;
update dop.user set set_level_time='2025-04-10 00:00:00' where lock_level=1;


-- reward_day 新增360订单amount1，新增180订单amount1
alter table dop.reward_day add column total_amount360 decimal(25,8) default 0 not null comment '新增360订单amount1';
alter table dop.reward_day add column total_amount180 decimal(25,8) default 0 not null comment '新增180订单amount1';

alter table dop.reward_day add column origin_total_dynamic decimal(25,8) default 0 not null comment '原始总动态';


update dop.product set pay_method='{"DOP": {"max": "3"}, "DOP+NAC": {"max": "2.8"}, "DOP+BIRD": {"max": "2.8"}, "DOP+FISH": {"max": "2.8"}, "DOP+FOMO": {"max": "2.8"}}' where id=4;
update dop.product set pay_method='{"DOP": {"max": "1.8"}}' where id=3;
update dop.product set pay_method='{"DOP": {"max": "3"}}' where id=5;
update dop.product set enable=0 where id=7;


insert into dop.product(id, type, name, pay_method, rate, day, enable) values (8, 1, '360天币', '{"DOP": {"max":"2.8"}}', 0.008, 360, 0);


alter table dop.reward_day add column un_stake_fee decimal(25,8) default 0 not null comment '解除质押手续费';
alter table dop.reward_day add column burn_amount decimal(25,8) default 0 not null comment '销毁数量';


# update dop.stake_user set burn_limit = GREATEST(stake_limit+burn_limit-total_static-total_dynamic, 0) where stake_limit+burn_limit > 0;


alter table dop.user add column team_perf decimal(25,8) default 0 not null comment '小区业绩';

update dop.user set set_level_time='2025-01-01 00:00:00' where lock_level=1 and set_level_time is null;

-- Add new columns to stake_user table for waiting status and shipping information
alter table dop.stake_user add column wait_status int not null default 0 comment '等待状态 0-无等待 1-等待中 2-已完成等待 3-已处理';
alter table dop.stake_user add column wait_days int not null default 0 comment '已等待天数';
alter table dop.stake_user add column stake_threshold decimal(25,8) null comment '满足的质押阈值';
alter table dop.stake_user add column receiver_name varchar(128) null comment '收货人姓名';
alter table dop.stake_user add column receiver_phone varchar(64) null comment '收货人联系方式';
alter table dop.stake_user add column receiver_address varchar(255) null comment '收货地址';

-- 添加今日解压扣减额度字段到stake_user表
-- 执行时间: 2025-07-04
-- 功能: 支持活动质押时恢复当天解压扣减的额度
alter table dop.stake_user add column today_unstake_deduction decimal(20,8) default 0.00000000 comment '今日解压扣减的额度';
