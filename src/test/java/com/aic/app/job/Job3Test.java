package com.aic.app.job;

import com.aic.app.admin.form.UserQuery;
import com.aic.app.model.UserModel;
import com.aic.app.model.UserWatch;
import com.aic.app.service.IUserWatchService;
import com.aic.app.vo.UserWatchVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@SpringBootTest
public class Job3Test {

    @Resource
    Job3 job3;
    @Resource
    IUserWatchService userWatchService;

    @Test
    public void test2() throws Exception {
        UserQuery form = new UserQuery();
        form.setId("f007f9b18c1444ad9f9c265c3ed61c00");
        LambdaQueryWrapper<UserWatch> qw = new LambdaQueryWrapper<>();
        List<UserWatchVo> list = userWatchService.list(qw).stream().map(UserWatchVo::new).toList();

        if (!list.isEmpty()) {
            job3.calcChildAmount2(list);
            // 计算完再过来显示的数据，不然会影响结算结果
            list = list.stream().filter(row -> {
                return StringUtils.isEmpty(form.getId()) || form.getId().equals(row.getId()) || form.getId().equals(row.getCode());
            }).toList();
        }

        for (UserWatchVo row : list) {
            System.out.println("===================");
            System.out.println(row.getAmount360DopNac() + " - " + row.getAmount360Nac() + " \t\t " + row.getAmount180DopNac() + " - " + row.getAmount180Nac());
            System.out.println(row.getAmount360DopFomo() + " - " + row.getAmount360Fomo() + " \t\t " + row.getAmount180DopFomo() + " - " + row.getAmount180Fomo());
        }

//        System.out.println(list.get(0));
    }

    @Test
    public void testCalcSumProductAmount() throws Exception {
        System.out.println("=== 测试 calcSumProductAmount 方法 ===");

        // 测试用户ID - 使用 DOP666 用户
//        String testUserId = "695a4ee509864a24a479e68c341a9530"; // DOP666 用户，有7个下级用户
//        String testUserId = "7bb1810c17464816abfddb48de3ade2a";
        String testUserId = "2bf1715fa05448b79d4a09dc9547097b";

        // 设置测试时间范围 - 使用更大的时间范围
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date beginTime = sdf.parse("2020-01-01");
        Date endTime = sdf.parse("2025-12-31");

        System.out.println("测试参数:");
        System.out.println("用户ID: " + testUserId);
        System.out.println("开始时间: " + sdf.format(beginTime));
        System.out.println("结束时间: " + sdf.format(endTime));
        System.out.println();

        // 调用方法
        UserModel result = job3.calcSumProductAmount(testUserId, beginTime, endTime);

        if (result == null) {
            System.out.println("结果为空 - 可能用户不存在或没有伞下用户");
            return;
        }

        System.out.println("=== 理财产品统计结果 ===");
        System.out.println("伞下活期理财: " + result.getProductCurrentAmount());
        System.out.println("伞下定期理财: " + result.getProductRegularAmount());
        System.out.println("30天理财: " + result.getProductAmount30());
        System.out.println("180天理财: " + result.getProductAmount180());
        System.out.println("360天理财: " + result.getProductAmount360());
        System.out.println();

        System.out.println("=== 新增锁仓统计结果 ===");
        System.out.println("锁仓静态收益 (类型2,3): " + result.getStakeStaticAmount());
        System.out.println("锁仓动态收益 (类型4): " + result.getStakeDynamicAmount());
        System.out.println("锁仓直推收益 (类型10): " + result.getStakeDirectAmount());
        System.out.println("灵活质押每天释放 (类型41): " + result.getStakeReleaseAmount());
        System.out.println();

        // 测试不指定时间范围的情况
        System.out.println("=== 测试不指定时间范围 ===");
        UserModel resultAll = job3.calcSumProductAmount(testUserId, null, null);
        if (resultAll != null) {
            System.out.println("全时间范围 - 锁仓静态收益: " + resultAll.getStakeStaticAmount());
            System.out.println("全时间范围 - 锁仓动态收益: " + resultAll.getStakeDynamicAmount());
            System.out.println("全时间范围 - 锁仓直推收益: " + resultAll.getStakeDirectAmount());
            System.out.println("全时间范围 - 灵活质押释放: " + resultAll.getStakeReleaseAmount());
        }

        System.out.println("=== 测试完成 ===");
    }

}
