package com.aic.app.job;

import com.aic.app.mapper.UserProductMapper;
import com.aic.app.model.*;
import com.aic.app.service.IUserService;
import com.aic.app.util.Utils;
import com.aic.app.vo.UserWatchVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
@Slf4j
public class Job3 {

    IUserService userService;
    UserProductMapper userProductMapper;
    
    /**
     * 计算伞下定期
     * @return
     */
    public Map<String, UserModel> calcChildAmount() {
        QueryWrapper<UserProduct> qw = new QueryWrapper<>();
        qw.in("status", 0, 1);
        qw.in("product_id", 1,2,3,4,5,6,7);
        qw.groupBy("user_id");
        List<UserModel> userModel = userProductMapper.findAllGroupByProduct(qw);
        Map<String, UserModel> userMap = userService.list(new LambdaQueryWrapper<User>().select(User::getId, User::getPid)).stream().collect(Collectors.toMap(User::getId, UserModel::new));

        for (UserModel row : userModel) {
            String pid = userMap.get(row.getId()).getPid();

            if (StringUtils.isBlank(pid)) {
                continue;
            }
            // 这里加一个pid去重，防止推荐关系循环，无线套娃
            Set<String> pidSet = new HashSet<>();
            
            while (true) {
                // 直推
                UserModel pUser = userMap.get(pid);
                if (StringUtils.isNotEmpty(pid) && pUser == null) {
                    log.error("[job1] no found user = {}", pid);
                }
                if (pUser == null || pidSet.contains(pid)) {
                    // 没有上级了就退出
                    break;
                }
                pidSet.add(pid);
                pUser.setProductCurrentAmount(pUser.getProductCurrentAmount().add(row.getProductCurrentAmount()));
                pUser.setProductRegularAmount(pUser.getProductRegularAmount().add(row.getProductRegularAmount()));
                pUser.setProductAmount30(pUser.getProductAmount30().add(row.getProductAmount30()));
                pUser.setProductAmount180(pUser.getProductAmount180().add(row.getProductAmount180()));
                pUser.setProductAmount360(pUser.getProductAmount360().add(row.getProductAmount360()));

                pUser.setAmount180Dop(pUser.getAmount180Dop().add(row.getAmount180Dop()));
                pUser.setAmount180Fish(pUser.getAmount180Fish().add(row.getAmount180Fish()));
                pUser.setAmount180Bird(pUser.getAmount180Bird().add(row.getAmount180Bird()));
                pUser.setAmount180Nac(pUser.getAmount180Nac().add(row.getAmount180Nac()));
                pUser.setAmount180Fomo(pUser.getAmount180Fomo().add(row.getAmount180Fomo()));

                pUser.setAmount360Dop(pUser.getAmount360Dop().add(row.getAmount360Dop()));
                pUser.setAmount360Fish(pUser.getAmount360Fish().add(row.getAmount360Fish()));
                pUser.setAmount360Bird(pUser.getAmount360Bird().add(row.getAmount360Bird()));
                pUser.setAmount360Nac(pUser.getAmount360Nac().add(row.getAmount360Nac()));
                pUser.setAmount360Fomo(pUser.getAmount360Fomo().add(row.getAmount360Fomo()));
                
                pid = pUser.getPid();
            }
        }

        return userMap;
    }

    /**
     * 计算极差用户统计
     * @return
     */
    public void calcChildAmount2(List<UserWatchVo> userWatchVoList) throws Exception {
        QueryWrapper<UserProduct> qw = new QueryWrapper<>();
        qw.in("status", 0, 1);
        qw.in("product_id", 1,2,3,4,5,7);
        qw.groupBy("user_id");
        List<UserModel> userModel = userProductMapper.findAllGroupByProduct(qw);
        Map<String, UserModel> userMap = userService.list(new LambdaQueryWrapper<User>().select(User::getId, User::getCode, User::getPid)).stream().collect(Collectors.toMap(User::getId, UserModel::new));

        // userWatchVoList stream to map, key = id, value = item
        Map<String, UserWatchVo> voMap = userWatchVoList.stream().collect(Collectors.toMap(UserWatch::getId, m -> m));
        
        for (UserModel row : userModel) {
            String pid = userMap.get(row.getId()).getPid();

            if (StringUtils.isBlank(pid)) {
                continue;
            }
            // 这里加一个pid去重，防止推荐关系循环，无线套娃
            Set<String> pidSet = new TreeSet<>();

            while (true) {
                // 直推
                UserModel pUser = userMap.get(pid);
                if (StringUtils.isNotEmpty(pid) && pUser == null) {
                    log.error("[job1] no found user = {}", pid);
                }
                if (pUser == null || pidSet.contains(pid)) {
                    // 没有上级了就退出
                    break;
                }
                pidSet.add(pid);
                pUser.setProductCurrentAmount(pUser.getProductCurrentAmount().add(row.getProductCurrentAmount()));
                pUser.setProductRegularAmount(pUser.getProductRegularAmount().add(row.getProductRegularAmount()));
                pUser.setProductAmount30(pUser.getProductAmount30().add(row.getProductAmount30()));
                pUser.setProductAmount180(pUser.getProductAmount180().add(row.getProductAmount180()));
                pUser.setProductAmount180U(pUser.getProductAmount180U().add(row.getProductAmount180U()));
                pUser.setProductAmount180B(pUser.getProductAmount180B().add(row.getProductAmount180B()));
                pUser.setProductAmount360(pUser.getProductAmount360().add(row.getProductAmount360()));
                pUser.setProductAmount360U(pUser.getProductAmount360U().add(row.getProductAmount360U()));
                pUser.setProductAmount360B(pUser.getProductAmount360B().add(row.getProductAmount360B()));

                pUser.setAmount180Dop(pUser.getAmount180Dop().add(row.getAmount180Dop()));
                pUser.setAmount180Fish(pUser.getAmount180Fish().add(row.getAmount180Fish()));
                pUser.setAmount180Bird(pUser.getAmount180Bird().add(row.getAmount180Bird()));
                pUser.setAmount180Nac(pUser.getAmount180Nac().add(row.getAmount180Nac()));
                pUser.setAmount180Fomo(pUser.getAmount180Fomo().add(row.getAmount180Fomo()));
                pUser.setAmount180DopBird(pUser.getAmount180DopBird().add(row.getAmount180DopBird()));
                pUser.setAmount180DopFish(pUser.getAmount180DopFish().add(row.getAmount180DopFish()));
                pUser.setAmount180DopNac(pUser.getAmount180DopNac().add(row.getAmount180DopNac()));
                pUser.setAmount180DopFomo(pUser.getAmount180DopFomo().add(row.getAmount180DopFomo()));

                pUser.setAmount360Dop(pUser.getAmount360Dop().add(row.getAmount360Dop()));
                pUser.setAmount360Fish(pUser.getAmount360Fish().add(row.getAmount360Fish()));
                pUser.setAmount360Bird(pUser.getAmount360Bird().add(row.getAmount360Bird()));
                pUser.setAmount360Nac(pUser.getAmount360Nac().add(row.getAmount360Nac()));
                pUser.setAmount360Fomo(pUser.getAmount360Fomo().add(row.getAmount360Fomo()));
                pUser.setAmount360DopBird(pUser.getAmount360DopBird().add(row.getAmount360DopBird()));
                pUser.setAmount360DopFish(pUser.getAmount360DopFish().add(row.getAmount360DopFish()));
                pUser.setAmount360DopNac(pUser.getAmount360DopNac().add(row.getAmount360DopNac()));
                pUser.setAmount360DopFomo(pUser.getAmount360DopFomo().add(row.getAmount360DopFomo()));
                
                pid = pUser.getPid();
            }
        }

        for (UserWatchVo row : userWatchVoList) {
            UserModel u = userMap.get(row.getId());
            row.setCode(u.getCode());
            row.setPid(u.getPid());
            row.setAmount30(u.getProductAmount30());
            row.setCurrent(u.getProductCurrentAmount());
            row.setAmount360B(u.getProductAmount360B());
            row.setAmount360U(u.getProductAmount360U());
            row.setAmount180B(u.getProductAmount180B());
            row.setAmount180U(u.getProductAmount180U());
            row.setAmount180(u.getProductAmount180());
            row.setAmount360(u.getProductAmount360());
            row.setAmount180Dop(u.getAmount180Dop());
            row.setAmount180Fish(u.getAmount180Fish());
            row.setAmount180Bird(u.getAmount180Bird());
            row.setAmount180Nac(u.getAmount180Nac());
            row.setAmount180Fomo(u.getAmount180Fomo());
            row.setAmount360Dop(u.getAmount360Dop());
            row.setAmount360Fish(u.getAmount360Fish());
            row.setAmount360Bird(u.getAmount360Bird());
            row.setAmount360Nac(u.getAmount360Nac());
            row.setAmount360Fomo(u.getAmount360Fomo());
            
            row.setAmount180DopFish(u.getAmount180DopFish());
            row.setAmount180DopBird(u.getAmount180DopBird());
            row.setAmount180DopNac(u.getAmount180DopNac());
            row.setAmount180DopFomo(u.getAmount180DopFomo());
            row.setAmount360DopFish(u.getAmount360DopFish());
            row.setAmount360DopBird(u.getAmount360DopBird());
            row.setAmount360DopNac(u.getAmount360DopNac());
            row.setAmount360DopFomo(u.getAmount360DopFomo());
            
        }

        Tree tree = new Tree();

        for (UserWatchVo row : userWatchVoList) {
            String pid = userMap.get(row.getId()).getPid();
            if (StringUtils.isBlank(pid)) {
                continue;
            }
            // 这里加一个pid去重，防止推荐关系循环，无线套娃
            TreeSet<String> pidSet = new TreeSet<>();
            while (true) {
                // 直推
                UserModel pUser = userMap.get(pid);
                if (StringUtils.isNotEmpty(pid) && pUser == null) {
                    log.error("[job1] no found user = {}", pid);
                }
                if (pUser == null || pidSet.contains(pid)) {
                    // 没有上级了就退出
                    break;
                }
                UserWatchVo watchVo = voMap.get(pid);
                if (watchVo != null) {
                    tree.addTree(pid, row.getId());
                    pidSet.add(pid);
                    break;
                }
                pid = pUser.getPid();
            }
        }

        voMap.forEach((key, value) -> {
            UserWatchVo watchVo = new UserWatchVo(value);
            BeanUtils.copyProperties(value, watchVo);
            voMap.put(key, watchVo);
        });

        for (UserWatchVo watchVo : userWatchVoList) {
            Node node = tree.getNode(watchVo.getId());
            if (node != null && !node.children.isEmpty()) {
                for (Node child : node.children) {
                    UserWatchVo row = voMap.get(child.id);
//                    System.out.println(watchVo.getId() + "-" + row.getId() +  " = " + watchVo.getAmount360U() + " - " + row.getAmount360U());
                    watchVo.setAmount30(watchVo.getAmount30().subtract(row.getAmount30()));
                    watchVo.setCurrent(watchVo.getCurrent().subtract(row.getCurrent()));
                    watchVo.setAmount360B(watchVo.getAmount360B().subtract(row.getAmount360B()));
                    watchVo.setAmount360U(watchVo.getAmount360U().subtract(row.getAmount360U()));
                    watchVo.setAmount180B(watchVo.getAmount180B().subtract(row.getAmount180B()));
                    watchVo.setAmount180U(watchVo.getAmount180U().subtract(row.getAmount180U()));
                    watchVo.setAmount180(watchVo.getAmount180().subtract(row.getAmount180()));
                    watchVo.setAmount360(watchVo.getAmount360().subtract(row.getAmount360()));
                    
                    watchVo.setAmount360Dop(watchVo.getAmount360Dop().subtract(row.getAmount360Dop()));
                    watchVo.setAmount360Fish(watchVo.getAmount360Fish().subtract(row.getAmount360Fish()));
                    watchVo.setAmount360Bird(watchVo.getAmount360Bird().subtract(row.getAmount360Bird()));
                    watchVo.setAmount180Dop(watchVo.getAmount180Dop().subtract(row.getAmount180Dop()));
                    watchVo.setAmount180Fish(watchVo.getAmount180Fish().subtract(row.getAmount180Fish()));
                    watchVo.setAmount180Bird(watchVo.getAmount180Bird().subtract(row.getAmount180Bird()));

                    watchVo.setAmount180DopFish(watchVo.getAmount180DopFish().subtract(row.getAmount180DopFish()));
                    watchVo.setAmount180DopBird(watchVo.getAmount180DopBird().subtract(row.getAmount180DopBird()));
                    watchVo.setAmount180DopNac(watchVo.getAmount180DopNac().subtract(row.getAmount180DopNac()));
                    watchVo.setAmount180DopFomo(watchVo.getAmount180DopFomo().subtract(row.getAmount180DopFomo()));
                    watchVo.setAmount180Fomo(watchVo.getAmount180Fomo().subtract(row.getAmount180Fomo()));
                    watchVo.setAmount180Nac(watchVo.getAmount180Nac().subtract(row.getAmount180Nac()));

                    watchVo.setAmount360DopFish(watchVo.getAmount360DopFish().subtract(row.getAmount360DopFish()));
                    watchVo.setAmount360DopBird(watchVo.getAmount360DopBird().subtract(row.getAmount360DopBird()));
                    watchVo.setAmount360DopNac(watchVo.getAmount360DopNac().subtract(row.getAmount360DopNac()));
                    watchVo.setAmount360DopFomo(watchVo.getAmount360DopFomo().subtract(row.getAmount360DopFomo()));
                    watchVo.setAmount360Fomo(watchVo.getAmount360Fomo().subtract(row.getAmount360Fomo()));
                    watchVo.setAmount360Nac(watchVo.getAmount360Nac().subtract(row.getAmount360Nac()));
                }
            }
        }
        
    }

    private void getSubordinateUsers(String userId, List<User> users, List<String> subordinateUsers) {
        for (User user : users) {
            if (user.getPid() != null && user.getPid().equals(userId)) {
                subordinateUsers.add(user.getId());
                getSubordinateUsers(user.getId(), users, subordinateUsers); // 递归获取子用户的子用户
            }
        }
    }
    
    // 计算伞下业绩
    public UserModel calcSumProductAmount(String userId, Date beginTime, Date endTime) {
        if (StringUtils.isEmpty(userId)) {
            return null;
        }
        List<User> userList = userService.list(new LambdaQueryWrapper<User>().select(User::getId, User::getCode, User::getPid));
        // 我要获取某个用户关系的所有用户ID
        List<String> ids = new ArrayList<>();
        getSubordinateUsers(userId, userList, ids);
        if (ids.isEmpty()) {
            return null;
        }

        // 统计理财产品数据
        QueryWrapper<UserProduct> qw = new QueryWrapper<>();
        if (beginTime != null && endTime != null) {
            qw.between("create_time", Utils.formatBeginTime(beginTime), Utils.formatEndTime(endTime));
        }
        qw.in("status", 0, 1);
        qw.in("user_id", ids);
        qw.in("product_id", 1,2,3,4,5,7);
        UserModel userModel = userProductMapper.sumProductAmount(qw);

        // 统计锁仓相关收益数据（通过user_log表）
        // 静态收益类型：2、3  动态收益：4  直推：10  每天释放：41
        QueryWrapper<Object> logQw = new QueryWrapper<>();
        if (beginTime != null && endTime != null) {
            logQw.between("ul.create_time", Utils.formatBeginTime(beginTime), Utils.formatEndTime(endTime));
        }
        logQw.in("ul.user_id", ids);
        logQw.in("ul.type", 2, 3, 4, 10, 41); // 包含相关收益类型
        UserModel stakeModel = userProductMapper.sumStakeLogAmount(logQw);

        // 合并锁仓数据到结果中
        if (userModel != null && stakeModel != null) {
            userModel.setStakeStaticAmount(stakeModel.getStakeStaticAmount());
            userModel.setStakeDynamicAmount(stakeModel.getStakeDynamicAmount());
            userModel.setStakeDirectAmount(stakeModel.getStakeDirectAmount());
            userModel.setStakeReleaseAmount(stakeModel.getStakeReleaseAmount());
        }

        return userModel;
    }

    // 计算伞下定期
    public Map<String, UserModel> calcSumProductAmount2(Collection<String> userIds) {
        if (userIds == null) {
            return null;
        }
        List<User> userList = userService.list(new LambdaQueryWrapper<User>().select(User::getId, User::getCode, User::getPid));
        
        Map<String, UserModel> userMap = new HashMap<>();
        
        for (String userId : userIds) {
            // 我要获取某个用户关系的所有用户ID
            List<String> ids = new ArrayList<>();
            getSubordinateUsers(userId, userList, ids);
            if (ids.isEmpty()) {
                continue;
            }
            QueryWrapper<UserProduct> qw = new QueryWrapper<>();
            Date now = new Date();
            qw.between("create_time", Utils.formatBeginTime(now), Utils.formatEndTime(now));
            qw.in("status", 0, 1);
            qw.in("user_id", ids);
            qw.in("product_id", 2,3,4,5,7);
            UserModel userModel = userProductMapper.sumProductAmount2(qw);
            userMap.put(userId, userModel);
        }
        
        return userMap;
    }
    
    static class Tree {
        Map<String, Node> nodeMap = new HashMap<>();
        Node getNode(String id) {
            return nodeMap.get(id);
        }
        void addTree(String pid, String id) {
            Node pNode = nodeMap.get(pid);
            if (pNode == null) {
                pNode = new Node(pid);
                nodeMap.put(pid, pNode);
            }
            Node node = nodeMap.get(id);
            if (node == null) {
                node = new Node(id);
            }
            pNode.addChild(node);
        }
    }
    
    static class Node {
        String id;
        List<Node> children;
        Node(String id) {
            this.id = id;
            this.children = new ArrayList<>();
        }
        void addChild(Node node) {
            children.add(node);
        }
    }

}

