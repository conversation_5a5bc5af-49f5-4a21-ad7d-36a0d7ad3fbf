package com.aic.app.job;

import com.aic.app.exception.BizException;
import com.aic.app.exception.Errors;
import com.aic.app.model.*;
import com.aic.app.service.*;
import com.aic.app.util.BizAssert;
import com.aic.app.util.Utils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Component
@Slf4j
@AllArgsConstructor
public class Job7 {

    IAssetService assetService;
    IUserService userService;
    IUserLogService userLogService;
    IStakeUserService stakeUserService;
    StringRedisTemplate stringRedisTemplate;
    ISysConfigService sysConfigService;
    IStakeUserProductService userProductService;
    IStakeRewardLogService rewardLogService;
    IUserAssetService userAssetService;
    private static final String LOCK_KEY_PREFIX = "dop:job_lock:job7";

    private static final boolean openLog = true;
    
    @Transactional(rollbackFor = Exception.class)
    public void run0() {
        execute(this::releaseStakeUserProduct);
        updateWaitingDays();
        updateActivityStakeDays();
    }
    
    protected void execute(Consumer<List<Asset>> callback) {
        log.info("[job7] 获取锁 {}", LOCK_KEY_PREFIX);
        Boolean acquired = stringRedisTemplate.opsForValue().setIfAbsent(LOCK_KEY_PREFIX, "1", 15*60, TimeUnit.SECONDS);
        log.info("[job7] 获取锁结果 {}", acquired);
        if (Boolean.FALSE.equals(acquired)) {
            log.info("[job7] 获取锁失败，任务不执行");
//            Utils.sendNotify("获取锁失败，任务不执行");
//            return;
        }
        try {
            Utils.setClose(true);
            log.info("[job7] 准备发放收益");
            // 10秒后再开始发，确保所有的请求都处理完了
//            if (!Utils.isDev()) {
//                Thread.sleep(10 * 1000);
//            }

            SysConfig sysConfig = sysConfigService.getSysConfig();
            List<String> times = sysConfig.getTimes();
            // 获取今天是星期几 dayOfWeek，1-7, 跟时区无关
//            int dayOfWeek = Calendar.getInstance().get(Calendar.DAY_OF_WEEK) - 1;

            // 获取今天是星期几 dayOfWeek，1-7, 跟时区无关
            Calendar now = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
            now.setFirstDayOfWeek(Calendar.MONDAY);
            int dayOfWeek = now.get(Calendar.DAY_OF_WEEK);
            if (dayOfWeek == Calendar.SUNDAY){
                dayOfWeek = 7;
            } else {
                dayOfWeek = dayOfWeek - 1;
            }
            
//            final BigDecimal rewardRate = sysConfig.getRewardRate();
            int index = (dayOfWeek - 1);
            log.info("[job7] 今天是星期{}, 获取第{}条配置", dayOfWeek, index);
            
            final BigDecimal rewardRate = new BigDecimal(times.get(index));
            log.info("[job7] 收益率 = {}", rewardRate);

            Map<String, StakeSumUser> userMap = new HashMap<>();
            List<User> users = userService.list();

            for (User user : users) {
                userMap.put(user.getId(), new StakeSumUser(user));
            }

            List<Asset> stakeAssets = assetService.listStakeAsset();

            Set<StakeUser> stakeUserSet = new HashSet<>();

            for (Asset asset : stakeAssets) {
                List<StakeUser> stakeUsers = stakeUserService.list(new LambdaQueryWrapper<StakeUser>().eq(StakeUser::getTokenId, asset.getTokenId()));
                // stakeUsers to map
                Map<String, StakeUser> stakeUserMap = stakeUsers.stream().collect(Collectors.toMap(StakeUser::getUserId, m -> m));

                // pre 清空昨日收益字段
                for (StakeUser stakeUser : stakeUsers) {
                    if (stakeUser.getTodayStatic().compareTo(BigDecimal.ZERO) > 0 || stakeUser.getTodayDynamic().compareTo(BigDecimal.ZERO) > 0) {
                        stakeUser.setTodayStatic(BigDecimal.ZERO);
                        stakeUser.setTodayDynamic(BigDecimal.ZERO);
                        stakeUser.setNodeReward(BigDecimal.ZERO);
                        stakeUserSet.add(stakeUser);
                    }
                    stakeUser.setPid(userMap.get(stakeUser.getUserId()).getPid());
                }
                
                // 0、先算池子的静态
                for (StakeUser stakeUser : stakeUsers) {
                    // 总算力
                    BigDecimal totalPower = stakeUser.getStaticPool();

                    if (totalPower.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal staticAmount = totalPower.multiply(rewardRate).setScale(8, RoundingMode.HALF_UP);
                        log.info("[job7] Token = {}, uid = {}, 质押池数量 = {}, 静态 = {}, 剩余额度 = {}", asset.getTokenId(), stakeUser.getUserId(), totalPower, staticAmount, stakeUser.getLimitAmount());

                        if (stakeUser.getLimitAmount().compareTo(BigDecimal.ZERO) > 0) {
                            BigDecimal realStatic = staticAmount;
                            if (realStatic.compareTo(stakeUser.getLimitAmount()) > 0) {
                                realStatic = stakeUser.getLimitAmount();
                            }
                            if (realStatic.compareTo(BigDecimal.ZERO) > 0) {
                                log.info("[job7] Token = {}, uid = {}, 实际静态 = {}", asset.getTokenId(), stakeUser.getUserId(), realStatic);
                                stakeUser.subLimit(realStatic);
                                // 先不进池子，算完动态再进池子
                                stakeUser.setNodeReward(realStatic);
                                stakeUser.setTotalStatic(stakeUser.getTotalStatic().add(realStatic));
                                stakeUserSet.add(stakeUser);
                                userLogService.save(new UserLog(stakeUser.getUserId(), UserLogType.StakeProfit.getValue(), totalPower, realStatic, asset.getTokenId(), asset.getTokenId(), UserLogType.StakeProfit.getLabel()));
                            }
                        } else {
                            log.info("[job7] Token = {}, uid = {}, 额度不足，无法获得静态", asset.getTokenId(), stakeUser.getUserId());
                        }

                    }
                }

                // 1、先给静态
                for (StakeUser stakeUser : stakeUsers) {
                    // 总算力
                    BigDecimal totalPower = stakeUser.getCurrentAmount();

                    if (totalPower.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal staticAmount = totalPower.multiply(rewardRate).setScale(8, RoundingMode.HALF_UP);
                        log.info("[job7] Token = {}, uid = {}, 质押数量 = {}, 静态 = {}, 剩余额度 = {}", asset.getTokenId(), stakeUser.getUserId(), totalPower, staticAmount, stakeUser.getLimitAmount());
                        
                        if (stakeUser.getLimitAmount().compareTo(BigDecimal.ZERO) > 0) {
                            BigDecimal realStatic = staticAmount;
                            if (realStatic.compareTo(stakeUser.getLimitAmount()) > 0) {
                                realStatic = stakeUser.getLimitAmount();
                            }
                            if (realStatic.compareTo(BigDecimal.ZERO) > 0) {
                                log.info("[job7] Token = {}, uid = {}, 实际静态 = {}", asset.getTokenId(), stakeUser.getUserId(), realStatic);
                                stakeUser.subLimit(realStatic);
                                stakeUser.setStaticPool(stakeUser.getStaticPool().add(realStatic));
                                stakeUser.setTodayStatic(realStatic);
                                stakeUser.setTotalStatic(stakeUser.getTotalStatic().add(realStatic));
                                stakeUserSet.add(stakeUser);
                                userLogService.save(new UserLog(stakeUser.getUserId(), UserLogType.StakeProfit.getValue(), totalPower, realStatic, asset.getTokenId(), asset.getTokenId(), UserLogType.StakeProfit.getLabel()));
                            }
                        } else {
                            log.info("[job7] Token = {}, uid = {}, 额度不足，无法获得静态", asset.getTokenId(), stakeUser.getUserId());
                        }
                        
                    }
                    
                }

                // 2、再给动态
                for (StakeUser stakeUser : stakeUsers) {
                    // 没有有上级或者没有静态
                    if (StringUtils.isBlank(stakeUser.getPid()) || stakeUser.getTodayStatic().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }

                    // 累计收益比例
                    BigDecimal sumRate = BigDecimal.ZERO;

                    // 这里加一个pid去重，防止推荐关系循环，无线套娃
                    Set<String> pidSet = new HashSet<>();
                    String cid = stakeUser.getUserId();
                    String pid = stakeUser.getPid();
                    while (true) {
                        // 直推
                        StakeSumUser pUser = userMap.get(pid);
                        if (StringUtils.isNotEmpty(pid) && pUser == null) {
                            log.error("[job7] no found user = {}", pid);
                        }
                        if (pUser == null || pidSet.contains(pid)) {
                            // 没有上级了就退出
                            break;
                        }
                        pidSet.add(pid);

                        StakeUser pStakeUser = stakeUserMap.get(pid);
                        if (pStakeUser == null) {
                            log.error("[job7] no found user = {}", pid);
                            break;
                        }

                        BigDecimal rate = getRate(pUser.user.getLevel());
                        BigDecimal rate2 = getRate(pUser.user.getRealLevel());
                        // 已经被拿完了，或者理财不足100，不能拿动态
                        if (rate.compareTo(sumRate) > 0) {
                            // 实际拿到收益比例
                            BigDecimal realRate = rate2.subtract(sumRate);
                            sumRate = rate;

                            if (realRate.compareTo(BigDecimal.ZERO) > 0 && pStakeUser.validNode()) {
                                // 直推动态
                                BigDecimal dynamic = stakeUser.getTodayStatic().multiply(realRate);
                                log.info("[job7] 动态返佣，下级用户 = {}, 上级用户 = {}, 等级 = {}, 真实等级 = {}, 等级比例 = {}, 实际拿到的比例 = {}, 动态 = {}", stakeUser.getUserId(), pid, pUser.user.getLevel(), pUser.user.getRealLevel(), rate, realRate, dynamic);

                                if (pStakeUser.getLimitAmount().compareTo(BigDecimal.ZERO) > 0) {
                                    BigDecimal realDynamic = dynamic;
                                    if (realDynamic.compareTo(pStakeUser.getLimitAmount()) > 0) {
                                        realDynamic = pStakeUser.getLimitAmount();
                                    }
                                    if (realDynamic.compareTo(BigDecimal.ZERO) > 0) {
                                        // 更新用户动态
                                        log.info("[job7] today( static {} + dynamic {} ) +  dynamic = {} gt redeemAmount = {}, relDynamic = {}", pStakeUser.getTodayStatic(), pStakeUser.getTodayDynamic(), dynamic, pStakeUser.getLimitAmount(), realDynamic);
                                        pStakeUser.subLimit(realDynamic);
                                        pStakeUser.setStaticPool(pStakeUser.getStaticPool().add(realDynamic));
                                        pStakeUser.setTodayDynamic(pStakeUser.getTodayDynamic().add(realDynamic));
                                        pStakeUser.setTotalDynamic(pStakeUser.getTotalDynamic().add(realDynamic));
                                        stakeUserSet.add(pStakeUser);
//                                        userLogService.save(new UserLog(pStakeUser.getUserId(), UserLogType.StakeReward.getValue(), stakeUser.getTodayStatic(), realDynamic, asset.getTokenId(), asset.getTokenId(), UserLogType.StakeReward.getLabel())
//                                                .setChildUserId(cid)
//                                                .setLevel(pUser.user.getLevel()));

                                        pUser.addChildAmount(cid, dynamic);
                                    }
                                }
                            }
                            
                        }
                        cid = pUser.user.getId();
                        pid = pUser.user.getPid();
                    }

                }

                List<UserLog> userLogs = new ArrayList<>();
                List<StakeRewardLog> rewardLogs = new ArrayList<>();

                // 动态流水
                for (StakeUser stakeUser : stakeUsers) {
                    if (stakeUser.getTodayDynamic().compareTo(BigDecimal.ZERO) > 0) {
                        UserLog userLog = new UserLog(stakeUser.getUserId(), UserLogType.StakeReward.getValue(), stakeUser.getSumAmount(),
                                stakeUser.getTodayDynamic(), UserLogType.StakeReward.getLabel())
                                .setSymbol(AssetEnum.DOP.getTokenId())
                                .setTokenId(AssetEnum.DOP.getTokenId());
                        userLogs.add(userLog);
                    }
                }
                
                // 3、10% 直推
                for (StakeUser stakeUser : stakeUsers) {
                    // 没有有上级或者没有静态
                    if (StringUtils.isBlank(stakeUser.getPid()) || stakeUser.getTodayStatic().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    StakeSumUser pUser = userMap.get(stakeUser.getPid());
                    if (pUser == null) {
                        log.error("[job7] 直推10% no found user = {}", stakeUser.getPid());
                        continue;
                    }
                    // 先累计
                    StakeUser pStakeUser = stakeUserMap.get(stakeUser.getPid());
                    if (pStakeUser == null) {
                        log.error("[job7] 直推10% no found user = {}", stakeUser.getPid());
                        continue;
                    }
                    if (!pStakeUser.validNode()) {
                        log.warn("[job7] 直推10% 下级用户 = {}, 静态 = {}, 上级用户 = {}, 质押数量为 = {}, 不满足最低质押，没有奖励", stakeUser.getUserId(), stakeUser.getTodayStatic(), pStakeUser.getUserId(), pStakeUser.getCurrentAmount());
                        continue;
                    }
                    BigDecimal rewardAmount = stakeUser.getTodayStatic().multiply(new BigDecimal("0.1"));
                    if (pStakeUser.getLimitAmount().compareTo(BigDecimal.ZERO) <= 0) {
                        log.warn("[job7] 直推10% 下级用户 = {}, 静态 = {}, 上级用户 = {}, 额度为0，没有奖励 = {}", stakeUser.getUserId(), stakeUser.getTodayStatic(), pStakeUser.getUserId(), rewardAmount);
                    }
                    BigDecimal realRewardAmount = rewardAmount;
                    if (rewardAmount.compareTo(pStakeUser.getLimitAmount()) > 0) {
                        realRewardAmount = pStakeUser.getLimitAmount();
                    }
                    if (realRewardAmount.compareTo(BigDecimal.ZERO) > 0) {
                        log.info("[job7] 直推10% 下级用户 = {}, 静态 = {}, 上级用户 = {}, 剩余额度 = {}, 动态奖励 = {}, 实际动态 = {}", stakeUser.getUserId(), stakeUser.getTodayStatic(), pStakeUser.getUserId(), pStakeUser.getLimitAmount(), rewardAmount, realRewardAmount);
                        pStakeUser.subLimit(realRewardAmount);
                        pStakeUser.setStaticPool(pStakeUser.getStaticPool().add(realRewardAmount));
                        pStakeUser.setTodayDynamic(pStakeUser.getTodayDynamic().add(realRewardAmount));
                        pStakeUser.setTotalDynamic(pStakeUser.getTotalDynamic().add(realRewardAmount));
                        stakeUserSet.add(pStakeUser);
                        userLogService.save(new UserLog(pStakeUser.getUserId(), UserLogType.StakeReward2.getValue(), stakeUser.getTodayStatic(), realRewardAmount, asset.getTokenId(), asset.getTokenId(), UserLogType.StakeReward2.getLabel())
                                .setChildUserId(stakeUser.getUserId())
                                .setLevel(stakeUser.getLevel()));

                        StakeSumUser stakeSumUser = userMap.get(stakeUser.getUserId());
                        User childUser = stakeSumUser.user;
                        StakeRewardLog rewardLog = new StakeRewardLog(pStakeUser.getUserId(), childUser, stakeUser, realRewardAmount).setSymbol(AssetEnum.DOP.getTokenId());
                        rewardLog.setProductAmount(stakeUser.getCurrentAmount());
                        rewardLog.setLevel(-1);
                        rewardLogs.add(rewardLog);
                        
                    }
                }


                for (StakeUser stakeUser : stakeUsers) {
                    // 把池子的静态都累计到池子
                    stakeUser.setStaticPool(stakeUser.getStaticPool().add(stakeUser.getNodeReward()));
                    stakeUser.setTodayStatic(stakeUser.getTodayStatic().add(stakeUser.getNodeReward()));
                    stakeUser.setNodeReward(BigDecimal.ZERO);

                    StakeSumUser sumUser = userMap.get(stakeUser.getUserId());
                    if (!sumUser.childAmount.isEmpty()) {
                        sumUser.childAmount.forEach((cid, amount) -> {
                            User childUser = userMap.get(cid).user;
                            StakeUser childStakeUser = stakeUserMap.get(cid);
                            StakeRewardLog rewardLog = new StakeRewardLog(stakeUser.getUserId(), childUser, childStakeUser, amount).setSymbol(AssetEnum.DOP.getTokenId());
                            rewardLogs.add(rewardLog);
                        });
                    }
                }

                if (!userLogs.isEmpty()) {
                    logInfo("保存流水");
                    userLogService.saveBatch(userLogs);
                }
                
                if (!rewardLogs.isEmpty()) {
                    logInfo("更新动态收益");
                    rewardLogService.saveBatch(rewardLogs);
                }

//                log.info("[job7] Token = {}, 数量 = {}", asset.getTokenId(), stakeUserSet.size() - size);
            }

            // 4、更新质押用户
            if (!stakeUserSet.isEmpty()) {
                log.info("[job7] 更新质押用户 数量 = {}", stakeUserSet.size());
                for (StakeUser stakeUser : stakeUserSet) {
                    stakeUserService.update(new LambdaUpdateWrapper<StakeUser>()
                            .set(StakeUser::getStaticPool, stakeUser.getStaticPool())
                            .set(StakeUser::getTotalStatic, stakeUser.getTotalStatic())
                            .set(StakeUser::getTotalDynamic, stakeUser.getTotalDynamic())
                            .set(StakeUser::getTodayStatic, stakeUser.getTodayStatic())
                            .set(StakeUser::getTodayDynamic, stakeUser.getTodayDynamic())
                            .set(StakeUser::getBurnLimit, stakeUser.getBurnLimit())
                            .eq(StakeUser::getId, stakeUser.getId())
                            .eq(StakeUser::getUserId, stakeUser.getUserId())
                            .eq(StakeUser::getTokenId, stakeUser.getTokenId()));
                }
            }

            // 5、重置今日解压扣减额度
            try {
                int resetCount = ((com.aic.app.service.impl.StakeUserServiceImpl) stakeUserService).getBaseMapper().resetTodayUnstakeDeduction();
                log.info("[job7] 重置今日解压扣减额度完成，影响记录数: {}", resetCount);
            } catch (Exception e) {
                log.error("[job7] 重置今日解压扣减额度失败", e);
            }

            // 6、更新质押记录状态
//            userStakeService.update(new LambdaUpdateWrapper<UserStake>()
//                    .set(UserStake::getStatus, 1)
//                    .eq(UserStake::getStatus, 0));
            
            if (callback != null) {
                log.info("[job7] 分期释放");
                callback.accept(stakeAssets);
                Utils.sendNotify("[job7] 分期释放");
            }
            Utils.sendNotify("[job7] 收益发放完成");

            log.info("[job7] 收益发放完成");
        } catch (BizException e) {
            log.error(e.getMsg());
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("[job7] 收益发放失败");
            Utils.sendNotify("[job7] 收益发放失败");
            throw new BizException(500, "收益发放失败");
        } finally {
            Utils.setClose(false);
            stringRedisTemplate.delete(LOCK_KEY_PREFIX);
            log.info("[job7] 释放锁");
        }
    }

    public void releaseStakeUserProduct(List<Asset> stakeAssets) {
        
        for (Asset asset : stakeAssets) {
            LambdaQueryWrapper<StakeUserProduct> qw = new LambdaQueryWrapper<StakeUserProduct>()
                    .eq(StakeUserProduct::getTokenId, asset.getTokenId())
                    .in(StakeUserProduct::getStatus, 1);
            List<StakeUserProduct> userProducts = userProductService.list(qw);
            
            // Map<String, BigDecimal> releaseMap = new TreeMap<>();

            for (StakeUserProduct userProduct : userProducts) {
                // 每日定期收益
                BigDecimal power = userProduct.getPower();
//                BigDecimal amount = power.multiply(userProduct.getRate()).setScale(8, RoundingMode.HALF_UP);
                BigDecimal amount = power.divide(BigDecimal.valueOf(userProduct.getDay()),8, RoundingMode.HALF_UP);
                if (userProduct.getReleaseDay() == userProduct.getDay() - 1) {
                    // 最后一天释放剩余的
//                    BigDecimal releaseAmount = amount.multiply(BigDecimal.valueOf(userProduct.getReleaseDay())).setScale(8, RoundingMode.HALF_UP);
//                    amount = userProduct.getPower().subtract(releaseAmount);
                    amount = userProduct.getPower().subtract(userProduct.getProfit());
                }
                userProduct.setReleaseDay(userProduct.getReleaseDay() + 1);
                userProduct.setProfit(userProduct.getProfit().add(amount));
                if (userProduct.getReleaseDay() >= userProduct.getDay()) {
                    log.info("[job7] 理财到期 id = {}, uid = {}, amount = {}, profit = {}", userProduct.getId(), userProduct.getUserId(),
                            userProduct.getPower(), userProduct.getProfit());
                    userProduct.setStatus(2);
                }

                if (amount.compareTo(BigDecimal.ZERO) > 0) {
                    log.info("[job7] 释放Token = {}, id = {}, uid = {}, power = {}, day = {}, amount = {}", asset.getTokenId(), userProduct.getId(), userProduct.getUserId(), userProduct.getPower(), userProduct.getReleaseDay(), amount);
                    // BigDecimal released = releaseMap.getOrDefault(userProduct.getUserId(), BigDecimal.ZERO);
                    String remark = String.format("%s, 总数量 = %s, 天数 = %d, 本次释放 = %s", UserLogType.StakeRelease.getLabel(), userProduct.getPower(), userProduct.getDay(), amount);
                    // userLogService.save(new UserLog(userProduct.getUserId(), UserLogType.StakeRelease.getValue(), userProduct.getAmount(), amount, asset.getTokenId(), asset.getTokenId(), remark));
                    userAssetService.plus(userProduct.getUserId(), asset.getTokenId(), amount, UserLogType.StakeRelease.getValue(), remark, asset.getTokenId());
                    // releaseMap.put(userProduct.getUserId(), released.add(amount));
                }
            }

            for (StakeUserProduct userProduct : userProducts) {
                boolean success = userProductService.update(new LambdaUpdateWrapper<StakeUserProduct>()
                        .set(userProduct.getStatus().equals(2), StakeUserProduct::getStatus, userProduct.getStatus())
                        .set(StakeUserProduct::getProfit, userProduct.getProfit())
                        .set(StakeUserProduct::getReleaseDay, userProduct.getReleaseDay())
                        .eq(StakeUserProduct::getId, userProduct.getId()));
                BizAssert.isTrue(success, () -> Errors.SERVER_EXCEPTION);
            }
            
            // log.info("[job7] 释放Token = {}, 数量 = {}", asset.getTokenId(), releaseMap.size());
            // if (!releaseMap.isEmpty()) {
            //     for (Map.Entry<String, BigDecimal> entry : releaseMap.entrySet()) {
            //         String userId = entry.getKey();
            //         BigDecimal amount = entry.getValue();
            //         log.info("[job7] 用户释放 = {}, uid = {}, amount = {}", asset.getTokenId(), userId, amount);
            //         boolean success = stakeUserService.update(new UpdateWrapper<StakeUser>()
            //                 .setSql("can_receive = can_receive + {0}", amount)
            //                 .eq("user_id", userId)
            //                 .eq("token_id", asset.getTokenId()));
            //         BizAssert.isTrue(success, () -> Errors.SERVER_EXCEPTION);
            //     }
            // }
            
        }
        
    }

    public static BigDecimal getRate(int level) {
        String[] targets = {"0", "0.05", "0.10", "0.15", "0.20", "0.25", "0.30", "0.40", "0.50", "0.60"};
        if (level >= targets.length) {
            level = targets.length - 1;
        }
        return new BigDecimal(targets[level]);
    }

    private static void logInfo(String s, Object... objects) {
        if (openLog) {
            log.info(s, objects);
        }
    }

    private void updateWaitingDays() {
        log.info("[job7] 更新等待天数");
        try {
            // 获取所有等待状态为1的用户
            List<StakeUser> waitingUsers = stakeUserService.list(new LambdaQueryWrapper<StakeUser>()
                    .eq(StakeUser::getWaitStatus, 1));
            
            if (waitingUsers.isEmpty()) {
                log.info("[job7] 没有处于等待状态的用户");
                return;
            }
            
            log.info("[job7] 处于等待状态的用户数量: {}", waitingUsers.size());
            
            for (StakeUser user : waitingUsers) {
                // 增加等待天数
                int newWaitDays = user.getWaitDays() + 1;
                
                // 如果等待天数达到15天，更新状态为已完成等待(2)
                if (newWaitDays >= 15) {
                    stakeUserService.update(new LambdaUpdateWrapper<StakeUser>()
                            .set(StakeUser::getWaitStatus, 2)
                            .set(StakeUser::getWaitDays, newWaitDays)
                            .eq(StakeUser::getId, user.getId()));
                    log.info("[job7] 用户 {} 完成15天等待期，状态更新为已完成", user.getUserId());
                } else {
                    // 否则只更新等待天数
                    stakeUserService.update(new LambdaUpdateWrapper<StakeUser>()
                            .set(StakeUser::getWaitDays, newWaitDays)
                            .eq(StakeUser::getId, user.getId()));
                    log.info("[job7] 用户 {} 等待天数更新为 {}", user.getUserId(), newWaitDays);
                }
            }
            
            log.info("[job7] 等待天数更新完成");
        } catch (Exception e) {
            log.error("[job7] 更新等待天数失败", e);
        }
    }

    /**
     * 更新活动质押天数
     */
    private void updateActivityStakeDays() {
        log.info("[job7] 更新活动质押天数");
        try {
            int updatedCount = activityService.updateStakeDays();
            log.info("[job7] 更新活动质押天数完成，更新记录数: {}", updatedCount);
        } catch (Exception e) {
            log.error("[job7] 更新活动质押天数失败", e);
        }
    }
}

class StakeSumUser {
    User user;
    StakeUser stakeUser;
    Map<String, BigDecimal> childAmount;

    /**
     * 小区业绩
     */
    BigDecimal teamPerf  = BigDecimal.ZERO;
    /**
     * 释放额度
     */
    BigDecimal limitAmount = BigDecimal.ZERO;

    public StakeSumUser(User user) {
        this.user = user;
        this.childAmount = new HashMap<>();
    }

    public void setStakeUser(StakeUser stakeUser) {
        this.stakeUser = stakeUser;
    }

    public void addChildAmount(String cid, BigDecimal amount) {
        BigDecimal sumAmount = childAmount.getOrDefault(cid, BigDecimal.ZERO).add(amount);
        childAmount.put(cid, sumAmount);
    }

    public void addTeamPerf(BigDecimal amount) {
        this.teamPerf = this.teamPerf.add(amount);
    }
    
    public String getPid() {
        if (user != null) {
            return user.getPid();
        }
        return null;
    }
    


}
