package com.aic.app.api;

import com.aic.app.form.ActivityBurnForm;
import com.aic.app.form.ActivityStakeForm;
import com.aic.app.model.ActivityConfig;
import com.aic.app.model.User;
import com.aic.app.model.UserActivityRecord;
import com.aic.app.service.IActivityService;
import com.aic.app.vo.Result;
import com.aic.app.vo.ActivityConfigVo;
import com.aic.app.vo.UserActivityRecordVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 活动控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/activity")
@Tag(name = "活动管理", description = "活动质押、销毁相关接口")
public class ActivityController {
    
    @Autowired
    private IActivityService activityService;
    
    @PostMapping("/stake")
    @Operation(summary = "活动质押")
    public Result<Boolean> activityStake(@RequestAttribute("user") User user, 
                                        @Valid @RequestBody ActivityStakeForm form) {
        activityService.activityStake(user, form);
        return Result.success(true);
    }
    
    @PostMapping("/unstake")
    @Operation(summary = "活动解除质押")
    public Result<Boolean> activityUnstake(@RequestAttribute("user") User user, 
                                          @Valid @RequestBody ActivityStakeForm form) {
        activityService.activityUnstake(user, form);
        return Result.success(true);
    }
    
    @PostMapping("/burn")
    @Operation(summary = "活动销毁")
    public Result<Boolean> activityBurn(@RequestAttribute("user") User user, 
                                       @Valid @RequestBody ActivityBurnForm form) {
        activityService.activityBurn(user, form);
        return Result.success(true);
    }
    
    @GetMapping("/status/{period}")
    @Operation(summary = "查询用户某期活动状态")
    public Result<UserActivityRecordVo> getActivityStatus(@RequestAttribute(value = "user", required = false) User user,
                                                         @PathVariable Integer period) {
        if (user == null) {
            // 为未登录用户返回默认的空状态
            UserActivityRecordVo vo = new UserActivityRecordVo();
            vo.setPeriod(period);
            vo.setStakeStatus(0);
            vo.setStakeStatusDesc("未质押");
            vo.setBurnStatus(0);
            vo.setBurnStatusDesc("未销毁");
            vo.setCompleted(0);
            vo.setCompletedDesc("未完成");
            return Result.success(vo);
        }
        UserActivityRecord record = activityService.getUserActivityStatus(user.getId(), period);
        UserActivityRecordVo vo;
        if (record != null) {
            vo = new UserActivityRecordVo(record);
        } else {
            // 用户已登录但没有质押过，返回默认状态
            vo = new UserActivityRecordVo();
            vo.setUserId(user.getId());
            vo.setPeriod(period);
            vo.setStakeStatus(0);
            vo.setStakeStatusDesc("未质押");
            vo.setBurnStatus(0);
            vo.setBurnStatusDesc("未销毁");
            vo.setCompleted(0);
            vo.setCompletedDesc("未完成");
        }
        return Result.success(vo);
    }
    
    @GetMapping("/records")
    @Operation(summary = "查询用户所有活动记录")
    public Result<List<UserActivityRecordVo>> getUserActivityRecords(@RequestAttribute(value = "user", required = false) User user) {
        if (user == null) {
            // 为未登录用户返回空列表，保持数据结构一致
            return Result.success(List.of());
        }
        List<UserActivityRecord> records = activityService.getUserActivityRecords(user.getId());
        List<UserActivityRecordVo> vos = records.stream()
                .map(UserActivityRecordVo::new)
                .collect(Collectors.toList());
        return Result.success(vos);
    }
    
    @GetMapping("/config/{period}")
    @Operation(summary = "查询活动配置")
    public Result<ActivityConfigVo> getActivityConfig(@PathVariable Integer period) {
        ActivityConfig config = activityService.getActivityConfigByPeriod(period);
        if (config == null) {
            return Result.success(null);
        }
        
        int participantCount = activityService.getParticipantCount(period);
        int completedCount = activityService.getCompletedCount(period);
        ActivityConfigVo vo = new ActivityConfigVo(config, participantCount, completedCount);
        return Result.success(vo);
    }
    
    @GetMapping("/current")
    @Operation(summary = "查询当前进行中的活动")
    public Result<ActivityConfigVo> getCurrentActivity() {
        ActivityConfig config = activityService.getCurrentActivity();
        if (config == null) {
            return Result.success(null);
        }
        
        int participantCount = activityService.getParticipantCount(config.getPeriod());
        int completedCount = activityService.getCompletedCount(config.getPeriod());
        ActivityConfigVo vo = new ActivityConfigVo(config, participantCount, completedCount);
        return Result.success(vo);
    }
}
