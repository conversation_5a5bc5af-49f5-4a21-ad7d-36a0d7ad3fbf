package com.aic.app.admin.api;

import com.aic.app.model.ActivityConfig;
import com.aic.app.model.UserActivityRecord;
import com.aic.app.service.IActivityService;
import com.aic.app.vo.ActivityUserRecordVo;
import com.aic.app.vo.Result;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 后台活动管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/activity")
@Hidden
@Tag(name = "后台活动管理", description = "后台活动管理相关接口")
public class AdminActivityController {
    
    @Autowired
    private IActivityService activityService;
    
    @GetMapping("/configs")
    @Operation(summary = "查询所有活动配置")
    public Result<IPage<ActivityConfig>> getActivityConfigs(@RequestParam(defaultValue = "1") Integer page,
                                                           @RequestParam(defaultValue = "10") Integer size) {
        Page<ActivityConfig> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<ActivityConfig> queryWrapper = new LambdaQueryWrapper<ActivityConfig>()
                .orderByDesc(ActivityConfig::getPeriod);
        IPage<ActivityConfig> result = activityService.page(pageParam, queryWrapper);
        return Result.success(result);
    }
    
    @PostMapping("/config")
    @Operation(summary = "创建活动配置")
    public Result<Boolean> createActivityConfig(@RequestBody ActivityConfig config) {
        boolean success = activityService.save(config);
        return Result.success(success);
    }
    
    @PutMapping("/config")
    @Operation(summary = "更新活动配置")
    public Result<Boolean> updateActivityConfig(@RequestBody ActivityConfig config) {
        boolean success = activityService.updateById(config);
        return Result.success(success);
    }
    
    @GetMapping("/users/{period}")
    @Operation(summary = "查询某期活动用户列表")
    public Result<List<ActivityUserRecordVo>> getActivityUsers(@PathVariable Integer period,
                                                              @RequestParam(required = false) String userIdOrCode,
                                                              @RequestParam(required = false) Integer burnStatus,
                                                              @RequestParam(required = false) Integer completed) {
        List<ActivityUserRecordVo> result = activityService.getActivityUserList(period, userIdOrCode, burnStatus, completed);
        return Result.success(result);
    }

    @GetMapping("/user/{userId}")
    @Operation(summary = "查询指定用户的活动记录")
    public Result<List<UserActivityRecord>> getUserActivityRecords(@PathVariable String userId) {
        List<UserActivityRecord> records = activityService.getUserActivityRecords(userId);
        return Result.success(records);
    }
    
    @GetMapping("/statistics/{period}")
    @Operation(summary = "查询某期活动统计信息")
    public Result<ActivityStatistics> getActivityStatistics(@PathVariable Integer period) {
        // 这里可以添加统计逻辑，比如参与人数、完成人数、总质押量、总销毁量等
        ActivityStatistics statistics = new ActivityStatistics();
        statistics.setPeriod(period);
        // TODO: 实现统计逻辑
        return Result.success(statistics);
    }
    
    /**
     * 活动统计信息VO
     */
    public static class ActivityStatistics {
        private Integer period;
        private Long totalParticipants;
        private Long completedParticipants;
        private java.math.BigDecimal totalStakeAmount;
        private java.math.BigDecimal totalBurnAmount;
        
        // getters and setters
        public Integer getPeriod() { return period; }
        public void setPeriod(Integer period) { this.period = period; }
        public Long getTotalParticipants() { return totalParticipants; }
        public void setTotalParticipants(Long totalParticipants) { this.totalParticipants = totalParticipants; }
        public Long getCompletedParticipants() { return completedParticipants; }
        public void setCompletedParticipants(Long completedParticipants) { this.completedParticipants = completedParticipants; }
        public java.math.BigDecimal getTotalStakeAmount() { return totalStakeAmount; }
        public void setTotalStakeAmount(java.math.BigDecimal totalStakeAmount) { this.totalStakeAmount = totalStakeAmount; }
        public java.math.BigDecimal getTotalBurnAmount() { return totalBurnAmount; }
        public void setTotalBurnAmount(java.math.BigDecimal totalBurnAmount) { this.totalBurnAmount = totalBurnAmount; }
    }
}
