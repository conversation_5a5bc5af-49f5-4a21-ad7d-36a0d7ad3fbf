package com.aic.app.admin.form;

import com.aic.app.form.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 活动用户查询表单
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ActivityUserQuery extends PageQuery {
    
    /**
     * 期数
     */
    @Schema(description = "期数")
    private Integer period;
    
    /**
     * UID或邀请码
     */
    @Schema(description = "UID或邀请码")
    private String userIdOrCode;
    
    /**
     * 销毁状态 0-未销毁 1-已销毁
     */
    @Schema(description = "销毁状态 0-未销毁 1-已销毁")
    private Integer burnStatus;
    
    /**
     * 完成状态 0-未完成 1-已完成
     */
    @Schema(description = "完成状态 0-未完成 1-已完成")
    private Integer completed;
}
