package com.aic.app.service;

import com.aic.app.form.ActivityBurnForm;
import com.aic.app.form.ActivityStakeForm;
import com.aic.app.model.ActivityConfig;
import com.aic.app.model.User;
import com.aic.app.model.UserActivityRecord;
import com.aic.app.vo.ActivityUserRecordVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 活动服务接口
 */
public interface IActivityService extends IService<ActivityConfig> {
    
    /**
     * 活动质押
     * @param user 用户
     * @param form 质押表单
     */
    void activityStake(User user, ActivityStakeForm form);
    
    /**
     * 活动解除质押
     * @param user 用户
     * @param form 质押表单
     */
    void activityUnstake(User user, ActivityStakeForm form);
    
    /**
     * 活动销毁
     * @param user 用户
     * @param form 销毁表单
     */
    void activityBurn(User user, ActivityBurnForm form);
    
    /**
     * 获取用户活动状态
     * @param userId 用户ID
     * @param period 期数
     * @return 用户活动记录
     */
    UserActivityRecord getUserActivityStatus(String userId, Integer period);
    
    /**
     * 获取用户所有活动记录
     * @param userId 用户ID
     * @return 活动记录列表
     */
    List<UserActivityRecord> getUserActivityRecords(String userId);
    
    /**
     * 获取某期已完成的用户列表
     * @param period 期数
     * @param page 分页参数
     * @return 已完成用户分页列表
     */
    IPage<UserActivityRecord> getCompletedUsersByPeriod(Integer period, Page<UserActivityRecord> page);
    
    /**
     * 根据期数获取活动配置
     * @param period 期数
     * @return 活动配置
     */
    ActivityConfig getActivityConfigByPeriod(Integer period);
    
    /**
     * 获取当前进行中的活动
     * @return 当前活动配置
     */
    ActivityConfig getCurrentActivity();
    
    /**
     * 计算用户当前活动质押总量
     * @param userId 用户ID
     * @param tokenId 代币ID
     * @return 活动质押总量
     */
    BigDecimal getUserActivityStakeAmount(String userId, String tokenId);
    
    /**
     * 检查活动人数限制
     * @param period 期数
     * @return 是否可以参与
     */
    boolean checkParticipantLimit(Integer period);
    
    /**
     * 获取活动参与人数统计
     * @param period 期数
     * @return 参与人数
     */
    int getParticipantCount(Integer period);

    /**
     * 更新质押天数（Job7定时任务使用）
     * @return 更新的记录数
     */
    int updateStakeDays();

    /**
     * 查询活动用户列表（管理员接口）
     * @param period 期数
     * @param userIdOrCode UID或邀请码
     * @param burnStatus 销毁状态
     * @param completed 完成状态
     * @return 用户列表
     */
    List<ActivityUserRecordVo> getActivityUserList(Integer period, String userIdOrCode,
                                                  Integer burnStatus, Integer completed);
    
    /**
     * 获取活动达成条件人数统计
     * @param period 期数
     * @return 达成条件人数
     */
    int getCompletedCount(Integer period);
}
