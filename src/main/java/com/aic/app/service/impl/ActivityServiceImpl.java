package com.aic.app.service.impl;

import com.aic.app.exception.Errors;
import com.aic.app.form.ActivityBurnForm;
import com.aic.app.form.ActivityStakeForm;
import com.aic.app.form.StakeForm;
import com.aic.app.mapper.ActivityConfigMapper;
import com.aic.app.mapper.UserActivityRecordMapper;
import com.aic.app.model.ActivityConfig;
import com.aic.app.model.StakeUser;
import com.aic.app.model.User;
import com.aic.app.model.UserActivityRecord;
import com.aic.app.service.IActivityService;
import com.aic.app.service.IStakeUserService;
import com.aic.app.service.IUserAssetService;
import com.aic.app.util.BizAssert;
import com.aic.app.model.UserLogType;
import com.aic.app.util.DistributedLock;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 活动服务实现类
 */
@Slf4j
@Service
public class ActivityServiceImpl extends ServiceImpl<ActivityConfigMapper, ActivityConfig> implements IActivityService {
    
    @Autowired
    private ActivityConfigMapper activityConfigMapper;
    
    @Autowired
    private UserActivityRecordMapper userActivityRecordMapper;
    
    @Autowired
    private IStakeUserService stakeUserService;
    
    @Autowired
    private IUserAssetService userAssetService;
    
    @Autowired
    private DistributedLock distributedLock;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activityStake(User user, ActivityStakeForm form) {
        String lockKey = "stake_period_" + form.getPeriod();
        distributedLock.executeWithLock(lockKey, 60, () -> {
            executeActivityStake(user, form);
        });
    }
    
    private void executeActivityStake(User user, ActivityStakeForm form) {
        log.info("活动质押开始 - 用户ID: {}, 期数: {}", user.getId(), form.getPeriod());
        
        // 1. 验证活动配置
        ActivityConfig activityConfig = getActivityConfigByPeriod(form.getPeriod());
        BizAssert.notNull(activityConfig, "活动配置不存在");

        // 2. 检查人数限制
        BizAssert.isTrue(checkParticipantLimit(form.getPeriod()), "活动参与人数已满");

        // 3. 获取或创建用户活动记录
        UserActivityRecord record = getUserActivityStatus(user.getId(), form.getPeriod());
        if (record == null) {
            record = new UserActivityRecord(user.getId(), form.getPeriod());
            userActivityRecordMapper.insert(record);
        }
        
        // 4. 检查是否已经质押
        BizAssert.isTrue(record.getStakeStatus() == 0, "该期已经质押，请先解除质押");
        
        // 5. 检查用户余额
        BigDecimal stakeAmount = activityConfig.getStakeAmount();
        BizAssert.isTrue(stakeAmount.compareTo(BigDecimal.ZERO) > 0, "质押数量必须大于0");
        
        // 6. 尝试恢复今日解压扣减的额度
        recoverTodayUnstakeDeduction(user.getId(), form.getTokenId(), stakeAmount);

        // 7. 执行新的活动质押逻辑
        Date stakeTime = new Date();
        int updateResult = userActivityRecordMapper.updateStakeStatus(user.getId(), form.getPeriod(), stakeTime, stakeAmount);
        BizAssert.isTrue(updateResult == 1, "更新活动质押状态失败");

        // 8. 调用原有质押接口
        StakeForm stakeForm = new StakeForm();
        stakeForm.setTokenId(form.getTokenId());
        stakeForm.setAmount(stakeAmount);
        stakeUserService.stake(user, stakeForm);
        
        log.info("活动质押完成 - 用户ID: {}, 期数: {}, 质押数量: {}", user.getId(), form.getPeriod(), stakeAmount);
    }

    /**
     * 恢复今日解压扣减的额度
     * @param userId 用户ID
     * @param tokenId 代币ID
     * @param stakeAmount 活动质押数量
     */
    private void recoverTodayUnstakeDeduction(String userId, String tokenId, BigDecimal stakeAmount) {
        try {
            StakeUser stakeUser = stakeUserService.getStakeUser(userId, tokenId);
            if (stakeUser != null && stakeUser.getTodayUnstakeDeduction() != null &&
                stakeUser.getTodayUnstakeDeduction().compareTo(BigDecimal.ZERO) > 0) {

                // 计算应该恢复的额度 = 质押数量 × 4（与解压扣减倍数一致）
                BigDecimal shouldRecoverAmount = stakeAmount.multiply(BigDecimal.valueOf(4));

                // 实际恢复额度 = min(应该恢复的额度, 今日扣减额度)
                BigDecimal actualRecoverAmount = shouldRecoverAmount.min(stakeUser.getTodayUnstakeDeduction());

                if (actualRecoverAmount.compareTo(BigDecimal.ZERO) > 0) {
                    int updateResult = ((StakeUserServiceImpl) stakeUserService).getBaseMapper().recoverUserLimitAmount(stakeUser.getId(), actualRecoverAmount);

                    if (updateResult == 1) {
                        log.info("恢复今日解压扣减额度成功 - 用户ID: {}, 质押数量: {}, 应恢复: {}, 实际恢复: {}, 剩余扣减: {}",
                            userId, stakeAmount, shouldRecoverAmount, actualRecoverAmount,
                            stakeUser.getTodayUnstakeDeduction().subtract(actualRecoverAmount));
                    } else {
                        log.warn("恢复今日解压扣减额度失败 - 用户ID: {}, 尝试恢复额度: {}", userId, actualRecoverAmount);
                    }
                } else {
                    log.info("无需恢复额度 - 用户ID: {}, 质押数量: {}, 今日扣减: {}", userId, stakeAmount, stakeUser.getTodayUnstakeDeduction());
                }
            }
        } catch (Exception e) {
            log.error("恢复今日解压扣减额度异常 - 用户ID: {}", userId, e);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activityUnstake(User user, ActivityStakeForm form) {
        String lockKey = "unstake_period_" + form.getPeriod();
        distributedLock.executeWithLock(lockKey, 60, () -> {
            executeActivityUnstake(user, form);
        });
    }
    
    private void executeActivityUnstake(User user, ActivityStakeForm form) {
        log.info("活动解除质押开始 - 用户ID: {}, 期数: {}", user.getId(), form.getPeriod());
        
        // 1. 验证活动配置
        ActivityConfig activityConfig = getActivityConfigByPeriod(form.getPeriod());
        BizAssert.notNull(activityConfig, "活动配置不存在");
        
        // 2. 获取用户活动记录
        UserActivityRecord record = getUserActivityStatus(user.getId(), form.getPeriod());
        BizAssert.notNull(record, "未找到活动记录");
        BizAssert.isTrue(record.getStakeStatus() == 1, "该期未质押");
        
        // 3. 执行新的活动解除质押逻辑
        int updateResult = userActivityRecordMapper.updateUnstakeStatus(user.getId(), form.getPeriod());
        BizAssert.isTrue(updateResult == 1, "更新活动解除质押状态失败");
        
        // 4. 调用原有解除质押接口
        StakeForm stakeForm = new StakeForm();
        stakeForm.setTokenId(form.getTokenId());
        stakeForm.setAmount(record.getStakeAmount());
        stakeUserService.unStake(user, stakeForm);
        
        log.info("活动解除质押完成 - 用户ID: {}, 期数: {}, 解除数量: {}", user.getId(), form.getPeriod(), record.getStakeAmount());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activityBurn(User user, ActivityBurnForm form) {
        String lockKey = "burn_period_" + form.getPeriod();
        distributedLock.executeWithLock(lockKey, 60, () -> {
            executeActivityBurn(user, form);
        });
    }
    
    private void executeActivityBurn(User user, ActivityBurnForm form) {
        log.info("活动销毁开始 - 用户ID: {}, 期数: {}", user.getId(), form.getPeriod());

        // 1. 验证活动配置
        ActivityConfig activityConfig = getActivityConfigByPeriod(form.getPeriod());
        BizAssert.notNull(activityConfig, "活动配置不存在");

        // 2. 获取或创建用户活动记录
        UserActivityRecord record = getUserActivityStatus(user.getId(), form.getPeriod());
        if (record == null) {
            record = new UserActivityRecord(user.getId(), form.getPeriod());
            userActivityRecordMapper.insert(record);
        }

        // 3. 检查是否已经销毁
        BizAssert.isTrue(record.getBurnStatus() == 0, "该期已经销毁");

        // 4. 检查销毁数量
        BigDecimal burnAmount = activityConfig.getBurnAmount();
        BizAssert.isTrue(burnAmount.compareTo(BigDecimal.ZERO) > 0, "销毁数量必须大于0");

        // 5. 检查用户DOP余额
        BizAssert.isTrue(userAssetService.getAsset(user.getId(), "DOP").checkBalance(burnAmount),
                        () -> Errors.BALANCE_EXCEPTION);

        // 6. 执行销毁操作
        userAssetService.pay(user.getId(), "DOP", "DOP", burnAmount,
                           UserLogType.ActivityBurn.getValue(), "活动销毁-第" + form.getPeriod() + "期");

        // 7. 更新活动记录
        Date burnTime = new Date();
        int updateResult = userActivityRecordMapper.updateBurnStatus(user.getId(), form.getPeriod(), burnTime, burnAmount);
        BizAssert.isTrue(updateResult == 1, "更新活动销毁状态失败");

        log.info("活动销毁完成 - 用户ID: {}, 期数: {}, 销毁数量: {}", user.getId(), form.getPeriod(), burnAmount);
    }

    @Override
    public UserActivityRecord getUserActivityStatus(String userId, Integer period) {
        return userActivityRecordMapper.getByUserIdAndPeriod(userId, period);
    }

    @Override
    public List<UserActivityRecord> getUserActivityRecords(String userId) {
        return userActivityRecordMapper.getUserActivityRecords(userId);
    }

    @Override
    public IPage<UserActivityRecord> getCompletedUsersByPeriod(Integer period, Page<UserActivityRecord> page) {
        return userActivityRecordMapper.getCompletedUsersByPeriod(period, page);
    }

    @Override
    public ActivityConfig getActivityConfigByPeriod(Integer period) {
        return activityConfigMapper.getByPeriod(period);
    }

    @Override
    public ActivityConfig getCurrentActivity() {
        Date now = new Date();
        return activityConfigMapper.selectOne(new QueryWrapper<ActivityConfig>()
                .le("start_time", now)
                .ge("end_time", now)
                .eq("status", 1)
                .orderByDesc("period")
                .last("limit 1"));
    }

    @Override
    public BigDecimal getUserActivityStakeAmount(String userId, String tokenId) {
        // 计算用户当前所有活动期数中正在质押的总数量
        return userActivityRecordMapper.getUserActivityStakeAmount(userId);
    }
    
    @Override
    public boolean checkParticipantLimit(Integer period) {
        ActivityConfig activityConfig = getActivityConfigByPeriod(period);
        if (activityConfig == null || activityConfig.getParticipantLimit() == null || activityConfig.getParticipantLimit() <= 0) {
            return true; // 无限制或配置不存在时允许参与
        }
        
        int currentParticipants = userActivityRecordMapper.getParticipantCount(period);
        return currentParticipants < activityConfig.getParticipantLimit();
    }
    
    @Override
    public int getParticipantCount(Integer period) {
        return userActivityRecordMapper.getParticipantCount(period);
    }
    
    @Override
    public int getCompletedCount(Integer period) {
        return userActivityRecordMapper.getCompletedCount(period);
    }

    @Override
    public int updateStakeDays() {
        return userActivityRecordMapper.updateStakeDays();
    }

    @Override
    public List<ActivityUserRecordVo> getActivityUserList(Integer period, String userIdOrCode,
                                                         Integer burnStatus, Integer completed) {
        List<ActivityUserRecordVo> list = userActivityRecordMapper.getActivityUserList(period, userIdOrCode, burnStatus, completed);

        // 设置状态描述
        for (ActivityUserRecordVo vo : list) {
            setStatusDescriptions(vo);
        }

        return list;
    }

    /**
     * 设置状态描述
     */
    private void setStatusDescriptions(ActivityUserRecordVo vo) {
        // 质押状态描述
        vo.setStakeStatusDesc((vo.getStakeStatus() != null && vo.getStakeStatus() == 1) ? "已质押" : "未质押");

        // 销毁状态描述
        vo.setBurnStatusDesc((vo.getBurnStatus() != null && vo.getBurnStatus() == 1) ? "已销毁" : "未销毁");

        // 完成状态描述
        if (vo.getCompleted() != null && vo.getCompleted() == 1) {
            vo.setCompletedDesc("已完成");
        } else {
            vo.setCompletedDesc("未完成");
        }
    }
}
