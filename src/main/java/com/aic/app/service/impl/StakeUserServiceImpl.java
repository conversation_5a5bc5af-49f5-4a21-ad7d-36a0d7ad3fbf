package com.aic.app.service.impl;

import com.aic.app.api.BurnForm;
import com.aic.app.exception.Errors;
import com.aic.app.form.ClaimForm;
import com.aic.app.form.RedeemForm;
import com.aic.app.form.ShippingInfoForm;
import com.aic.app.form.StakeForm;
import com.aic.app.mapper.StakeUserMapper;
import com.aic.app.mapper.UserMapper;
import com.aic.app.mapper.UserRelationMapper;
import com.aic.app.model.*;
import com.aic.app.sdk.ApiUser;
import com.aic.app.service.*;
import com.aic.app.util.BizAssert;
import com.aic.app.util.Utils;
import com.aic.app.vo.MyStakeDataVo;
import com.aic.app.vo.RankVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class StakeUserServiceImpl extends ServiceImpl<StakeUserMapper, StakeUser> implements IStakeUserService {

    @Resource
    IProductService productService;
    @Resource
    IStakeProductService stakeProductService;
    @Resource
    IStakeUserProductService userProductService;
    @Resource
    IUserLogService userLogService;
    @Resource
    IUserAssetService userAssetService;
    @Resource
    IUserService userService;
    @Resource
    UserMapper userMapper;
    @Resource
    IUserStakeService userStakeService;
    @Resource
    IAssetService assetService;
    @Resource
    UserRelationMapper userRelationMapper;
    @Resource
    INodeBuyRecordService nodeBuyRecordService;
    @Resource
    @Lazy
    IActivityService activityService;

    @Override
    public com.aic.app.vo.UserAsset getUserAsset(String uid) {
        com.aic.app.vo.UserAsset userAsset = new com.aic.app.vo.UserAsset();
        List<UserAsset> phoneAssets = userAssetService.listPhoneAsset(uid);
        for (UserAsset phoneAsset : phoneAssets) {
            userAsset.setAsset(phoneAsset.getTokenId(), phoneAsset.getBalance());
        }
        return userAsset;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserLog redeemCurrent(StakeUser user, RedeemForm form) {

        return null;
    }

    public User initUser(String userId, String code, String pid) {
        User user = new User(userId);
        user.setCode(code);
        user.setPid(pid);
        user.setOpenid(userId);
        userService.save(user);
        userAssetService.initAsset(user.getId(), this::saveBatch);
        return user;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public User checkUser(ApiUser apiUser) {
        if (apiUser != null) {
            User user = userService.findByOpenId(apiUser.openId());
            // 没有就插入
            if (user == null) {
                user = initUser(apiUser.openId(), null, null);
            } else {
                final String userId = user.getId();
                userAssetService.checkAsset(user.getId(), (list) -> this.checkStakeUser(userId, list));
            }
            return user;
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void checkStakeUser(String userId, List<Asset> list) {
        List<StakeUser> userStakes = this.list(new LambdaQueryWrapper<StakeUser>().eq(StakeUser::getUserId, userId));
        List<StakeUser> newUserStakes = new ArrayList<>();
        for (Asset asset : list) {
            if (asset.getStake()) {
                // 如果不存在userStakes中，就创建一个
                StakeUser stakeUser = userStakes.stream().filter(row -> row.getTokenId().equals(asset.getTokenId())).findFirst().orElse(null);
                if (stakeUser == null) {
                    stakeUser = new StakeUser();
                    stakeUser.setUserId(userId);
                    stakeUser.setTokenId(asset.getTokenId());
                    newUserStakes.add(stakeUser);
                }
            }
        }
        // 如果有新的，就批量保存
        if (!newUserStakes.isEmpty()) {
            this.saveBatch(newUserStakes);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean withdraw(StakeUser user, String tokenId, BigDecimal amount) {
        BigDecimal canReceive = user.getCanReceive();
        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.RECEIVE_EXCEPTION);
        BizAssert.isTrue(canReceive.compareTo(amount) >= 0, () -> Errors.RECEIVE_EXCEPTION);
        UserAsset asset = userAssetService.getAsset(user.getUserId(), tokenId);
        log.info("领取收益 UID = {}, amount = {}", user.getId(), amount);
        boolean result = update(new UpdateWrapper<StakeUser>()
                .setSql("can_receive = can_receive - {0}", amount)
                .apply("can_receive - {0} >= 0", amount)
                .eq("id", user.getId()));
        BizAssert.isTrue(result, () -> Errors.SERVER_EXCEPTION);
        return userAssetService.plus(user.getUserId(), asset.getTokenId(), amount, UserLogType.RECEIVE.getValue(), UserLogType.RECEIVE.getLabel(), asset.getTokenId());
    }

    @Override
    public IPage<UserModel> findAllForAdminUser(Page<User> page, QueryWrapper<User> queryWrapper) {
        return baseMapper.findAllForAdminUser(page, queryWrapper);
    }

    @Override
    public IPage<StakeUserModel> findAllForAdmin(Page<StakeUser> page, QueryWrapper<StakeUser> queryWrapper) {
        return baseMapper.findAllForAdmin(page, queryWrapper);
    }

    @Override
    public void updateCodeAndPid(String id, String code, String pid) {
//        log.info("[user] sync user id = {}, code = {}, pid = {}", id, code, pid);
//        this.update(new LambdaUpdateWrapper<StakeUser>()
//                .set(code != null, StakeUser::getCode, code)
//                .set(pid != null, StakeUser::getPid, pid)
//                .eq(StakeUser::getId, id));
    }
    
    @Override
    public StakeUser getStakeUser(String userId, String tokenId) {
        StakeUser stakeUser = this.getOne(new LambdaQueryWrapper<StakeUser>()
                .eq(StakeUser::getUserId, userId)
                .eq(StakeUser::getTokenId, tokenId));
        if (stakeUser == null) {
            return new StakeUser();
        }
        return stakeUser;
    }

    @Override
    public MyStakeDataVo getMyProduct(User user, String tokenId) {
        StakeUser stakeUser = null;
        if (user != null) {
            stakeUser = getStakeUser(user.getId(), tokenId);
        }
        Asset asset = assetService.getByTokenId(tokenId);
        MyStakeDataVo data = new MyStakeDataVo(user, stakeUser, asset);
        if (user != null) {
            if (ObjectUtils.isNotEmpty(user.getPid())
                    && !Long.valueOf(0).equals(user.getPid())) {

                User pUser = userMapper.selectById(user.getPid());
                if (pUser != null) {
                    data.setRefCode(pUser.getCode());
                }
            }
            UserAsset userAsset = userAssetService.getAsset(user.getId(), tokenId);
           UserAsset juAsset = userAssetService.getAsset(user.getId(), AssetEnum.JU.getTokenId());
            data.setBalance(userAsset.getBalance());
           data.setJuBalance(juAsset.getBalance());
            long invitation = userRelationMapper.countChild(user.getId());
            long teamCount = userRelationMapper.countTeam(user.getId());
            data.setInvitation(invitation);
            data.setTeamCount(teamCount);
        }
        
        // 添加等待信息
        data.setWaitStatus(stakeUser.getWaitStatus());
        data.setWaitDays(stakeUser.getWaitDays());
        data.setStakeThreshold(stakeUser.getStakeThreshold());
        data.setReceiverName(stakeUser.getReceiverName());
        data.setReceiverPhone(stakeUser.getReceiverPhone());
        data.setReceiverAddress(stakeUser.getReceiverAddress());
        
        // 计算可解除质押额度
        if (user != null && stakeUser != null) {
            BigDecimal totalStake = stakeUser.getPendingAmount().add(stakeUser.getCurrentAmount());
            BigDecimal activityStakeAmount = activityService.getUserActivityStakeAmount(user.getId(), tokenId);
            BigDecimal availableUnstakeAmount = totalStake.subtract(activityStakeAmount);
            // 确保不为负数
            if (availableUnstakeAmount.compareTo(BigDecimal.ZERO) < 0) {
                availableUnstakeAmount = BigDecimal.ZERO;
            }
            data.setAvailableUnstakeAmount(availableUnstakeAmount);
        }
        
        return data;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stake(User user, StakeForm form) {
        BigDecimal amount = form.getAmount();
        String token = form.getTokenId();

        BizAssert.notNull(user.getPid(), () -> Errors.NO_INVITE_EXCEPTION);

        log.info("质押 uid = {}, token = {}, amount = {}", user.getId(), token, amount);
        UserAsset asset = userAssetService.getAsset(user.getId(), token);
        BizAssert.notNull(asset, () -> Errors.BALANCE_EXCEPTION);
        BizAssert.isTrue(asset.checkBalance(amount), () -> Errors.BALANCE_EXCEPTION);
        
        StakeUser stakeUser = getStakeUser(user.getId(), form.getTokenId());

        // 扣款
        userAssetService.pay(user.getId(), asset.getTokenId(), asset.getTokenId(), amount, UserLogType.Stake.getValue(), UserLogType.Stake.getLabel() + "【" + token + "】");
        
        int updateRow = baseMapper.updateUserCurrentAmount(stakeUser.getId(), amount);
        BizAssert.isTrue(updateRow == 1, () -> Errors.BALANCE_EXCEPTION);
        
        // 检查是否满足等待条件，且当前不在等待状态
        if (stakeUser.getWaitStatus() == null || stakeUser.getWaitStatus() == 0) {
            if (amount.compareTo(new BigDecimal("5000")) >= 0) {
                // 设置等待状态为1，等待天数为0，记录满足的阈值为5000
                baseMapper.updateWaitStatus(stakeUser.getId(), 1, 0, new BigDecimal("5000"));
                log.info("用户 {} 质押达到5000，进入等待状态", user.getId());
            } else if (amount.compareTo(new BigDecimal("3000")) >= 0) {
                // 设置等待状态为1，等待天数为0，记录满足的阈值为3000
                baseMapper.updateWaitStatus(stakeUser.getId(), 1, 0, new BigDecimal("3000"));
                log.info("用户 {} 质押达到3000，进入等待状态", user.getId());
            } else if (amount.compareTo(new BigDecimal("1500")) >= 0) {
                // 设置等待状态为1，等待天数为0，记录满足的阈值为1500
                baseMapper.updateWaitStatus(stakeUser.getId(), 1, 0, new BigDecimal("1500"));
                log.info("用户 {} 质押达到1500，进入等待状态", user.getId());
            }
        }
        
        if (!Boolean.TRUE.equals(stakeUser.getStakeFirst())) {
            // 首次质押增加4倍额度
            BigDecimal limit = amount.multiply(BigDecimal.valueOf(4));
            updateRow = baseMapper.updateUserLimitAmount(stakeUser.getId(), limit);
            BizAssert.isTrue(updateRow == 1, () -> Errors.SERVER_EXCEPTION);
        }
        
        UserStake userStake = new UserStake(0, user.getId(), asset.getTokenId(), amount);
        userStake.setStatus(1);
        userStakeService.save(userStake);
        
        // this.updateTeamPerf(user.getId(), asset.getTokenId(), amount);

        assetService.updateTotal(asset.getTokenId(), amount, null, null, null, null, null, null);
        
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unStake(User user, StakeForm form) {
        BigDecimal amount = form.getAmount();
        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.AMOUNT_EXCEPTION);
        StakeUser stakeUser = getStakeUser(user.getId(), form.getTokenId());
        UserAsset asset = userAssetService.getAsset(user.getId(), form.getTokenId());

        BigDecimal totalStake = stakeUser.getPendingAmount().add(stakeUser.getCurrentAmount());
        
        // 计算用户当前活动质押总量
        BigDecimal activityStakeAmount = activityService.getUserActivityStakeAmount(user.getId(), form.getTokenId());
        
        // 计算可解除质押数量 = 总质押 - 活动质押
        BigDecimal availableUnstakeAmount = totalStake.subtract(activityStakeAmount);
        
        BizAssert.isTrue(totalStake.compareTo(amount) >= 0, () -> Errors.AMOUNT_EXCEPTION);
        BizAssert.isTrue(availableUnstakeAmount.compareTo(amount) >= 0, "可解除质押数量不足，参与活动质押的数量需通过活动解除质押接口解除");
        
        BigDecimal currentAmount = BigDecimal.ZERO;
        BigDecimal pendingAmount = BigDecimal.ZERO;
        
        if (stakeUser.getPendingAmount().compareTo(BigDecimal.ZERO) > 0) {
            if (stakeUser.getPendingAmount().compareTo(amount) >= 0) {
                pendingAmount = amount;
            } else {
                pendingAmount = stakeUser.getPendingAmount();
                currentAmount = amount.subtract(pendingAmount);
            }
        } else {
            currentAmount = amount;
        }
        
        log.info("[stake] 赎回质押 资产 = {}, UID = {}, amount = {}, 总质押 = {}, 活动质押 = {}, 可解除 = {}, 扣减待确认 = {}, 扣减已确认 = {}", 
                form.getTokenId(), user.getId(), amount, totalStake, activityStakeAmount, availableUnstakeAmount, pendingAmount, currentAmount);
        boolean result = update(new UpdateWrapper<StakeUser>()
                .setSql("current_amount = current_amount - {0}, pending_amount = pending_amount - {1}", currentAmount, pendingAmount)
                .apply("current_amount - {0} >= 0 and pending_amount >= pending_amount - {1}", currentAmount, pendingAmount)
                .eq("id", stakeUser.getId()));
        BizAssert.isTrue(result, () -> Errors.SERVER_EXCEPTION);

        // 解除质押 扣除2%手续费
        BigDecimal realAmount = amount.multiply(new BigDecimal("0.98"));
        
        userAssetService.plus(user.getId(), asset.getTokenId(), realAmount, UserLogType.UnStake.getValue(), UserLogType.UnStake.getLabel(), asset.getTokenId());
        
        if (stakeUser.getStakeLimit().compareTo(BigDecimal.ZERO) > 0) {
            // 解除质押扣除4倍额度，并记录今日扣减额度
            BigDecimal limit = amount.multiply(BigDecimal.valueOf(4));
            if (limit.compareTo(stakeUser.getStakeLimit()) > 0) {
                limit = stakeUser.getStakeLimit();
            }
            int updateRow = baseMapper.updateUserLimitAmountWithDeduction(stakeUser.getId(), limit);
            BizAssert.isTrue(updateRow == 1, () -> Errors.SERVER_EXCEPTION);

            log.info("解除质押扣减额度 - 用户ID: {}, 解除数量: {}, 扣减额度: {}", user.getId(), amount, limit);
        }
        
        UserStake userStake = new UserStake(1, user.getId(), asset.getTokenId(), amount);
        userStake.setStatus(1);
        userStakeService.save(userStake);

        // this.updateTeamPerf(user.getId(), asset.getTokenId(), amount.negate());

        assetService.updateTotal(asset.getTokenId(), amount.negate(), null, null, null, null, null, null);
        
        // 解除质押后，如果用户在等待状态1，则重置等待状态
        if (stakeUser.getWaitStatus() != null && stakeUser.getWaitStatus() == 1) {
            baseMapper.resetWaitStatus(stakeUser.getId());
            log.info("用户 {} 解除质押，重置等待状态", user.getId());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void claimWeek(User user, ClaimForm form) {
        BigDecimal amount = form.getAmount();
        StakeUser stakeUser = getStakeUser(user.getId(), form.getTokenId());
        BigDecimal canReceive = stakeUser.getWeekDynamic();
        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.RECEIVE_EXCEPTION);
        BizAssert.isTrue(canReceive.compareTo(amount) >= 0, () -> Errors.RECEIVE_EXCEPTION);
        UserAsset asset = userAssetService.getAsset(user.getId(), form.getTokenId());
        log.info("领取周分红 UID = {}, amount = {}", user.getId(), amount);
        boolean result = update(new UpdateWrapper<StakeUser>()
                .setSql("week_dynamic = week_dynamic - {0}", amount)
                .apply("week_dynamic - {0} >= 0", amount)
                .eq("id", stakeUser.getId()));
        BizAssert.isTrue(result, () -> Errors.SERVER_EXCEPTION);
        userAssetService.plus(user.getId(), asset.getTokenId(), amount, UserLogType.WithdrawWeek.getValue(), UserLogType.WithdrawWeek.getLabel(), asset.getTokenId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void claimStatic(User user, ClaimForm form) {
        StakeProduct product = stakeProductService.getById(form.getProductId());
        BizAssert.notNull(product, "产品不存在");
        
        BigDecimal amount = form.getAmount();

        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.REQUEST_EXCEPTION);

        String tokenId = form.getTokenId();
        StakeUser stakeUser = getStakeUser(user.getId(), tokenId);
        
        BizAssert.isTrue(stakeUser.getStaticPool().compareTo(amount) >= 0, () -> Errors.WITHDRAW_BALANCE_EXCEPTION);

        BigDecimal juPrice = productService.getPrice(AssetEnum.JU.getTokenId());
        BigDecimal bnbPrice = productService.getPrice(AssetEnum.BNB.getTokenId());
        BigDecimal tokenPrice = productService.getPrice(tokenId);
        // 手续费 30%
        BigDecimal feeToken = amount.multiply(product.getFee()).setScale(8, RoundingMode.HALF_UP);
//        // 扣除的 ju = token 数量 * token价格 / ju价格
//        BigDecimal fee = feeToken.multiply(tokenPrice).divide(juPrice, 8, RoundingMode.HALF_UP);
//        // 等价的BNB = token 数量 * token价格 / bnb价格 
        BigDecimal bnbFee = feeToken.multiply(tokenPrice).divide(bnbPrice, 8, RoundingMode.HALF_UP);

        final BigDecimal fee = feeToken;
        final BigDecimal power = amount;

        if (fee.compareTo(BigDecimal.ZERO) > 0) {
            UserAsset asset = userAssetService.getAsset(user.getId(), AssetEnum.DOP.getTokenId());
            BizAssert.isTrue(asset.getBalance().compareTo(fee) >= 0, () -> Errors.WITHDRAW_BALANCE_EXCEPTION2);
            String remark = String.format("提取静态收益，产品：%s，数量：%s，手续费：%s, JU单价：%s, %s单价: %s，bnbPrice：%s", product.getName(), amount, fee, juPrice, tokenId, tokenPrice, bnbPrice);
            userAssetService.pay(user.getId(), tokenId, asset.getTokenId(), fee, UserLogType.WithdrawFee.getValue(), remark);
        }
        
        log.info("[stake] 提取静态 token = {} => {}, amount = {}, fee = {}, power = {}", tokenId, product.getName(), amount, fee, power);

        int updateRow = baseMapper.updateStaticPool(stakeUser.getId(), amount);
        BizAssert.isTrue(updateRow == 1, () -> Errors.WITHDRAW_BALANCE_EXCEPTION);

        StakeUserProduct userProduct = new StakeUserProduct(user.getId(), Utils.genOrderNo(), product, power);
        userProduct.setAmount(amount);
        userProduct.setFee(fee);
        userProduct.setType(0);
        userProduct.setTokenId(tokenId);
//        userProduct.setPrice1(juPrice);
        userProduct.setPrice2(tokenPrice);
        userProduct.setFeeToken(feeToken);
        userProduct.setFeeTokenId(AssetEnum.DOP.getTokenId());
        userProduct.setBnbFee(bnbFee);
        userProductService.save(userProduct);

        claimStaticFee(userProduct);
    }
    
    public void claimStaticFee(StakeUserProduct userProduct) {
        BigDecimal fee = userProduct.getFeeToken();
        if (fee.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("[stake] 没有静态手续费，无需销毁 = {}", userProduct);
            return;
        }
        // 回购资金池 75%
        BigDecimal supply = fee.multiply(new BigDecimal("0.75")).setScale(8, RoundingMode.HALF_UP);
        // 社区共建者 分红 20%
        BigDecimal totalWeekPool = BigDecimal.ZERO;
        // 社区共建者 分红 20%
        BigDecimal nodePool = fee.multiply(new BigDecimal("0.2")).setScale(8, RoundingMode.HALF_UP);
        // totalSupplyBnb
        BigDecimal totalSupplyBnb;
        if (AssetEnum.BNB.getTokenId().equals(userProduct.getFeeTokenId())) {
            totalSupplyBnb = userProduct.getFee();
        } else {
            totalSupplyBnb = userProduct.getBnbFee();
        }
        // 打印log
        log.info("[stake] 静态手续费 = {}, supply = {}, totalWeekPool = {}, nodePool = {}", fee, supply, totalWeekPool, nodePool);
        assetService.updateTotal(userProduct.getTokenId(), null, supply, null, null, totalWeekPool, nodePool, totalSupplyBnb);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateTeamPerf(Long userId, String tokenId, BigDecimal amount) {
        // Set<Long> userIds = new HashSet<>();
        // userIds.add(userId);
        
        // UserRelation userRelation = userRelationMapper.selectById(userId);
        // if (userRelation != null && StringUtils.isNotEmpty(userRelation.getPath())) {
        //     String[] ids = userRelation.getPath().substring(1).split("/");
        //     for (String id : ids) {
        //         userIds.add(Long.parseLong(id));
        //     }
        // }
        // log.info("[stake] 更新团队业绩 userId = {}, tokenId = {}, amount = {}, ids = {}", userId, tokenId, amount, userIds);
        // this.update(new UpdateWrapper<StakeUser>()
        //         .setSql("team_perf = team_perf + {0}", amount)
        //         .eq("token_id", tokenId)
        //         .in("user_id", userIds));
    }

    @Override
    public List<RankVo> listTeamPerfTop(String tokenId, int size) {
        return this.baseMapper.listTeamPerfTop(tokenId, size);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addWeekDynamic(StakeUser stakeUser) {
        boolean result = this.update(new UpdateWrapper<StakeUser>()
//                .setSql("week_dynamic = week_dynamic + {0}, can_receive = can_receive + {1}, old_team_perf = team_perf - old_team_perf, team_perf = 0", stakeUser.getWeekDynamic(), stakeUser.getCanReceive())
                .setSql("week_dynamic = week_dynamic + {0}", stakeUser.getWeekDynamic())
                .eq("id", stakeUser.getId())
                .eq("user_id", stakeUser.getUserId()));
        userLogService.addLog(stakeUser.getUserId(), UserLogType.WeekDynamic.getValue(), stakeUser.getWeekDynamic(), UserLogType.WeekDynamic.getLabel(), stakeUser.getTokenId(), stakeUser.getTokenId());
        BizAssert.isTrue(result, () -> Errors.SERVER_EXCEPTION);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void setMaxTeamPerf(String tokenId) {
        this.update(new UpdateWrapper<StakeUser>()
                .setSql("max_team_perf = GREATEST(team_perf, max_team_perf)")
                .eq("token_id", tokenId));
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void buyNode(User user, Asset asset) {
        String tokenId = asset.getTokenId();
        String userId = user.getId();
        String payToken = AssetEnum.DOP.getTokenId();
        UserAsset userAsset = userAssetService.getAsset(userId, payToken);
        BizAssert.notNull(userAsset, () -> Errors.BALANCE_EXCEPTION);
        BizAssert.isTrue(userAsset.checkBalance(asset.getNodePrice()), () -> Errors.BALANCE_EXCEPTION);
        log.info("购买节点 UID = {}, PID = {}, tokenId = {}", userId, user.getPid(), tokenId);
        userAssetService.pay(userId, asset.getTokenId(), payToken, asset.getNodePrice(), UserLogType.BuyNode.getValue(), UserLogType.BuyNode.getLabel() + "【" + tokenId + "】");
        
        StakeUser stakeUser = getStakeUser(user.getId(), tokenId);
        this.update(new LambdaUpdateWrapper<StakeUser>()
                .setSql("node = node + 1")
                .eq(BaseEntity::getId, stakeUser.getId()));

        // 记录购买记录
        NodeBuyRecord nodeBuyRecord = new NodeBuyRecord();
        nodeBuyRecord.setUserId(userId);
        nodeBuyRecord.setTokenId(tokenId);
        nodeBuyRecord.setQuantity(1);
        nodeBuyRecord.setState(0);
        nodeBuyRecordService.save(nodeBuyRecord);

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void withdrawNodeReward(User user, Asset asset, BigDecimal amount) {
        // 检查余额
        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.REQUEST_EXCEPTION);

        StakeUser stakeUser = getStakeUser(user.getId(), asset.getTokenId());
        BizAssert.isTrue(stakeUser.getNodeReward().compareTo(amount) >= 0, () -> Errors.WITHDRAW_BALANCE_EXCEPTION);
        log.info("提现节点返佣 UID = {}, amount = {}", user.getId(), amount);
        // 更新余额
        this.update(new LambdaUpdateWrapper<StakeUser>()
                .setSql("node_reward = node_reward - {0}", amount)
                .apply("node_reward - {0} >= 0", amount)
                .eq(BaseEntity::getId, stakeUser.getId()));
        // 提现
        userLogService.addLog(user.getId(), UserLogType.BuyNodeReward.getValue(), amount.negate(), UserLogType.BuyNodeReward.getLabel(), AssetEnum.BNB.getTokenId(), asset.getTokenId());
        userAssetService.plus(user.getId(), AssetEnum.BNB.getTokenId(), amount, UserLogType.WithdrawNodeReward.getValue(), UserLogType.WithdrawNodeReward.getLabel(), asset.getTokenId());   
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void withdrawNodePool(User user, Asset asset, BigDecimal amount) {
        // 提取节点池
        BizAssert.isTrue(amount.compareTo(BigDecimal.ZERO) > 0, () -> Errors.REQUEST_EXCEPTION);

        StakeUser stakeUser = getStakeUser(user.getId(), asset.getTokenId());
        BizAssert.isTrue(stakeUser.getNodePool().compareTo(amount) >= 0, () -> Errors.WITHDRAW_BALANCE_EXCEPTION);
        log.info("提现节点池 UID = {}, amount = {}", user.getId(), amount);
        // 更新余额
        this.update(new LambdaUpdateWrapper<StakeUser>()
                .setSql("node_pool = node_pool - {0}", amount)
                .apply("node_pool - {0} >= 0", amount)
                .eq(BaseEntity::getId, stakeUser.getId()));
        // 提现
        String withdrawTokenId = AssetEnum.JU.getTokenId();
        userAssetService.plus(user.getId(), withdrawTokenId, amount, UserLogType.WithdrawNodePool.getValue(), UserLogType.WithdrawNodePool.getLabel(), withdrawTokenId);
    }

    @Override
    public List<StakeModel> listStakeUsers(String tokenId) {
        return this.baseMapper.listStakeUsers(tokenId);
    }

    @Override
    public void setMaxNodePerf(String tokenId) {
        this.update(new UpdateWrapper<StakeUser>()
                .setSql("max_node_perf = GREATEST(max_node_perf, node_perf)")
                .eq("token_id", tokenId));
    }

    @Override
    public void addNodePool(StakeUser stakeUser, BigDecimal amount) {
//        boolean result = this.update(new UpdateWrapper<StakeUser>()
//                .setSql("node_pool = node_pool + {0}", amount)
//                .eq("user_id", stakeUser.getUserId())
//                .eq("token_id", stakeUser.getTokenId()));
//
//        userLogService.addLog(stakeUser.getUserId(), UserLogType.NodeDividend.getValue(), amount, UserLogType.NodeDividend.getLabel(), stakeUser.getTokenId(), stakeUser.getTokenId());
//        BizAssert.isTrue(result, () -> Errors.SERVER_EXCEPTION);

//        String tokenId = AssetEnum.JU.getTokenId();
        String tokenId = AssetEnum.DOP.getTokenId();
        userAssetService.plus(stakeUser.getUserId(), tokenId, amount, UserLogType.NodeDividend.getValue(), UserLogType.NodeDividend.getLabel(), tokenId);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void burn(User user, BurnForm form) {
        // 检查余额
        BizAssert.isTrue(form.getAmount().compareTo(BigDecimal.ZERO) > 0, () -> Errors.REQUEST_EXCEPTION);
        UserAsset asset = userAssetService.getAsset(user.getId(), AssetEnum.DOP.getTokenId());
        // 检查余额
        BizAssert.isTrue(asset.checkBalance(form.getAmount()), () -> Errors.BURN_EXCEPTION);
        // 销毁
        userAssetService.pay(user.getId(), AssetEnum.DOP.getTokenId(), AssetEnum.DOP.getTokenId(), form.getAmount(), UserLogType.Burn.getValue(), UserLogType.Burn.getLabel());
        // 增加销毁4倍额度
        BigDecimal burnAmount = form.getAmount().multiply(BigDecimal.valueOf(4));  
        StakeUser stakeUser = getStakeUser(user.getId(), AssetEnum.DOP.getTokenId());
        // 增加销毁4倍额度
        this.update(new LambdaUpdateWrapper<StakeUser>()
                .setSql("burn_limit = burn_limit + {0}", burnAmount)
                .eq(BaseEntity::getId, stakeUser.getId()));
                
                
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitShippingInfo(User user, ShippingInfoForm form) {
        StakeUser stakeUser = getStakeUser(user.getId(), form.getTokenId());
        BizAssert.notNull(stakeUser, "用户质押记录不存在");
        
        // 检查用户是否已完成等待期
//        BizAssert.isTrue(stakeUser.getWaitStatus() != null && stakeUser.getWaitStatus() == 2, 
//                "用户未完成等待期，不能提交收货信息");
        
        // 更新收货信息
        return update(new LambdaUpdateWrapper<StakeUser>()
                .set(StakeUser::getReceiverName, form.getReceiverName())
                .set(StakeUser::getReceiverPhone, form.getReceiverPhone())
                .set(StakeUser::getReceiverAddress, form.getReceiverAddress())
                .eq(StakeUser::getId, stakeUser.getId()));
    }
}
