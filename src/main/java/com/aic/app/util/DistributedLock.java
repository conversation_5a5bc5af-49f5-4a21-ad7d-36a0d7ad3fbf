package com.aic.app.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 分布式锁工具类
 */
@Component
@Slf4j
public class DistributedLock {
    
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    
    private static final String LOCK_PREFIX = "dop:activity_lock:";
    private static final int DEFAULT_TIMEOUT = 30; // 默认锁超时时间30秒
    
    /**
     * 执行带锁的操作
     * @param lockKey 锁键
     * @param timeoutSeconds 超时时间（秒）
     * @param supplier 要执行的操作
     * @return 操作结果
     */
    public <T> T executeWithLock(String lockKey, int timeoutSeconds, Supplier<T> supplier) {
        String fullLockKey = LOCK_PREFIX + lockKey;
        log.info("尝试获取分布式锁: {}", fullLockKey);
        
        Boolean acquired = stringRedisTemplate.opsForValue().setIfAbsent(
            fullLockKey, 
            String.valueOf(System.currentTimeMillis()), 
            timeoutSeconds, 
            TimeUnit.SECONDS
        );
        
        if (Boolean.TRUE.equals(acquired)) {
            log.info("成功获取分布式锁: {}", fullLockKey);
            try {
                return supplier.get();
            } finally {
                stringRedisTemplate.delete(fullLockKey);
                log.info("释放分布式锁: {}", fullLockKey);
            }
        } else {
            log.warn("获取分布式锁失败: {}", fullLockKey);
            throw new RuntimeException("系统繁忙，请稍后重试");
        }
    }
    
    /**
     * 执行带锁的操作（使用默认超时时间）
     * @param lockKey 锁键
     * @param supplier 要执行的操作
     * @return 操作结果
     */
    public <T> T executeWithLock(String lockKey, Supplier<T> supplier) {
        return executeWithLock(lockKey, DEFAULT_TIMEOUT, supplier);
    }
    
    /**
     * 执行带锁的操作（无返回值）
     * @param lockKey 锁键
     * @param timeoutSeconds 超时时间（秒）
     * @param runnable 要执行的操作
     */
    public void executeWithLock(String lockKey, int timeoutSeconds, Runnable runnable) {
        executeWithLock(lockKey, timeoutSeconds, () -> {
            runnable.run();
            return null;
        });
    }
    
    /**
     * 执行带锁的操作（无返回值，使用默认超时时间）
     * @param lockKey 锁键
     * @param runnable 要执行的操作
     */
    public void executeWithLock(String lockKey, Runnable runnable) {
        executeWithLock(lockKey, DEFAULT_TIMEOUT, runnable);
    }
} 