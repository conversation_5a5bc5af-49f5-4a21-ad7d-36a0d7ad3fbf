package com.aic.app.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
@NoArgsConstructor
public class UserModel extends User {
    int layer;
    /**
     * 伞下活期理财
     */
    BigDecimal productCurrentAmount;

    /**
     * 30天理财
     */
    BigDecimal productAmount30;

    /**
     * 180天理财
     */
    BigDecimal productAmount180;

    /**
     * 180天U理财
     */
    BigDecimal productAmount180U;

    /**
     * 180天B理财
     */
    BigDecimal productAmount180B;

    /**
     * 360天理财
     */
    BigDecimal productAmount360;
    /**
     * 360天U理财
     */
    BigDecimal productAmount360U;
    /**
     * 360天B理财
     */
    BigDecimal productAmount360B;

    /**
     * 伞下定期
     */
    BigDecimal productRegularAmount;
    
    BigDecimal amount180Dop;
    BigDecimal amount180Bird;
    BigDecimal amount180DopBird;
    BigDecimal amount180Fish;
    BigDecimal amount180DopFish;

    BigDecimal amount180Nac = BigDecimal.ZERO;
    BigDecimal amount180DopNac = BigDecimal.ZERO;
    BigDecimal amount180Fomo = BigDecimal.ZERO;
    BigDecimal amount180DopFomo = BigDecimal.ZERO;

    BigDecimal amount360Dop;
    BigDecimal amount360Fish;
    BigDecimal amount360Bird;
    BigDecimal amount360DopFish;
    BigDecimal amount360DopBird;

    BigDecimal amount360Nac = BigDecimal.ZERO;
    BigDecimal amount360DopNac = BigDecimal.ZERO;
    BigDecimal amount360Fomo = BigDecimal.ZERO;
    BigDecimal amount360DopFomo = BigDecimal.ZERO;
    
    // 直推收益
    BigDecimal parentDop;
    BigDecimal parentFish;
    BigDecimal parentBird;
    BigDecimal parentNac;
    BigDecimal parentFomo;
    
    // 伞下今日累计购买定期
    BigDecimal sumAmountDop = BigDecimal.ZERO;
    BigDecimal sumAmountFish = BigDecimal.ZERO;
    BigDecimal sumAmountBird = BigDecimal.ZERO;
    BigDecimal sumAmountNac = BigDecimal.ZERO;
    BigDecimal sumAmountFomo = BigDecimal.ZERO;
    BigDecimal sumAmountDopFish = BigDecimal.ZERO;
    BigDecimal sumAmountDopBird = BigDecimal.ZERO;
    BigDecimal sumAmountDopNac = BigDecimal.ZERO;
    BigDecimal sumAmountDopFomo = BigDecimal.ZERO;

    // 锁仓相关统计
    /**
     * 伞下锁仓静态收益
     */
    BigDecimal stakeStaticAmount = BigDecimal.ZERO;

    /**
     * 伞下锁仓动态收益
     */
    BigDecimal stakeDynamicAmount = BigDecimal.ZERO;

    /**
     * 伞下锁仓直推收益
     */
    BigDecimal stakeDirectAmount = BigDecimal.ZERO;

    /**
     * 伞下灵活质押每天释放
     */
    BigDecimal stakeReleaseAmount = BigDecimal.ZERO;

    public UserModel(User user) {
        this.setId(user.getId());
        this.setCode(user.getCode());
        this.setPid(user.getPid());
        this.productCurrentAmount = BigDecimal.ZERO;
        this.productRegularAmount = BigDecimal.ZERO;
        this.productAmount30 = BigDecimal.ZERO;
        this.productAmount180 = BigDecimal.ZERO;
        this.productAmount180U = BigDecimal.ZERO;
        this.productAmount180B = BigDecimal.ZERO;
        this.productAmount360 = BigDecimal.ZERO;
        this.productAmount360U = BigDecimal.ZERO;
        this.productAmount360B = BigDecimal.ZERO;
        this.amount180Dop = BigDecimal.ZERO;
        this.amount180Fish = BigDecimal.ZERO;
        this.amount180Bird = BigDecimal.ZERO;
        this.amount360Dop = BigDecimal.ZERO;
        this.amount360Fish = BigDecimal.ZERO;
        this.amount360Bird = BigDecimal.ZERO;
        this.amount180DopFish = BigDecimal.ZERO;
        this.amount180DopBird = BigDecimal.ZERO;
        this.amount360DopFish = BigDecimal.ZERO;
        this.amount360DopBird = BigDecimal.ZERO;

        // 初始化锁仓统计字段
        this.stakeStaticAmount = BigDecimal.ZERO;
        this.stakeDynamicAmount = BigDecimal.ZERO;
        this.stakeDirectAmount = BigDecimal.ZERO;
        this.stakeReleaseAmount = BigDecimal.ZERO;
    }
    
    public String getParentAmount() {
        if (parentDop == null) {
            parentDop = BigDecimal.ZERO;
        }
        if (parentFish == null) {
            parentFish = BigDecimal.ZERO;
        }
        if (parentBird == null) {
            parentBird = BigDecimal.ZERO;
        }
        if (parentNac == null) {
            parentNac = BigDecimal.ZERO;
        }
        if (parentFomo == null) {
            parentFomo = BigDecimal.ZERO;
        }
        return parentDop.setScale(2, RoundingMode.HALF_UP) + " DOP\n" +
        parentFish.setScale(2, RoundingMode.HALF_UP) + " FISH\n" + 
        parentBird.setScale(2, RoundingMode.HALF_UP) + " BIRD\n" + 
        parentNac.setScale(2, RoundingMode.HALF_UP) + " NAC\n" + 
        parentFomo.setScale(2, RoundingMode.HALF_UP) + " FOMO";
    }
}
