package com.aic.app.vo;

import com.aic.app.model.Asset;
import com.aic.app.model.StakeUser;
import com.aic.app.model.User;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class MyStakeDataVo {
    
    @Schema(description = "用户ID")
    private String userId;
    
    @Schema(description = "BNB余额")
    private BigDecimal bnbBalance;

    @Schema(description = "JU余额")
    private BigDecimal juBalance;
    
    @Schema(description = "余额")
    private BigDecimal balance;
    
    @Schema(description = "tokenId")
    private String tokenId;

    @Schema(description = "质押数量(待确认)")
    BigDecimal pendingAmount = BigDecimal.ZERO;
    @Schema(description = "质押数量(已确认)")
    BigDecimal currentAmount = BigDecimal.ZERO;
    @Schema(description = "我的邀请码")
    String code;
    @Schema(description = "输入邀请码")
    String refCode;

    /**
     * 静态池子
     */
    @Schema(description = "静态池子")
    private BigDecimal staticPool = BigDecimal.ZERO;
    /**
     * 动态池子
     */
    @Schema(description = "动态池子")
    private BigDecimal dynamicPool = BigDecimal.ZERO;

    @Schema(description = "可领取奖励")
    BigDecimal canReceive = BigDecimal.ZERO;

    @Schema(description = "可领取奖励(合计)")
    BigDecimal totalCanReceive = BigDecimal.ZERO;

    @Schema(description = "团队人数")
    long teamCount;
    
    @Schema(description = "直推人数")
    long invitation;

    /**
     * 周分红
     */
    @Schema(description = "周分红")
    private BigDecimal weekDynamic = BigDecimal.ZERO;
    
    @Schema(description = "产出剩余额度")
    private BigDecimal limit = BigDecimal.ZERO;

    @Schema(description = "可解除质押额度(总质押-活动质押)")
    private BigDecimal availableUnstakeAmount = BigDecimal.ZERO;

    // 总质押
    @Schema(description = "总质押")
    private BigDecimal totalStaked;
    // 回购池
    @Schema(description = "回购池")
    private BigDecimal totalSupply;
    // 销毁池
    @Schema(description = "销毁池")
    private BigDecimal totalBurn;
    // dao池
    @Schema(description = "dao池")
    private BigDecimal totalDao;

    // 回购池BNB
    @Schema(description = "回购池BNB")
    private BigDecimal totalSupplyBnb;

    @Schema(description = "ido地址")
    private String idoAddress;
    @Schema(description = "质押地址")
    private String stakeAddress;
    @Schema(description = "token地址")
    private String tokenAddress;

    @Schema(description = "JU价格")
    private BigDecimal juPrice;
//    @Schema(description = "BNB价格")
//    private BigDecimal bnbPrice;
    @Schema(description = "TOKEN价格")
    private BigDecimal tokenPrice;

    // ==============================
    @Schema(description = "我的级别")
    int level;

    @Schema(description = "今日静态")
    BigDecimal todayStatic = BigDecimal.ZERO;
    @Schema(description = "累计静态")
    BigDecimal totalStatic = BigDecimal.ZERO;

    @Schema(description = "今日动态")
    BigDecimal todayDynamic = BigDecimal.ZERO;
    @Schema(description = "累计动态")
    BigDecimal totalDynamic = BigDecimal.ZERO;

    @Schema(description = "今日购买理财")
    BigDecimal todayBuy = BigDecimal.ZERO;
    @Schema(description = "累计购买理财")
    BigDecimal totalBuy = BigDecimal.ZERO;

    @Schema(description = "可领取定期收益")
    BigDecimal canReceiveProfit = BigDecimal.ZERO;

    @Schema(description = "真实等级")
    int realLevel;
    
    @Schema(description = "是否达标：true-已达标 false-未达标")
    boolean istouch;

    /**
     * 等待状态 0-无等待 1-等待中 2-已完成等待 3-已处理
     */
    @Schema(description = "等待状态 0-无等待 1-等待中 2-已完成等待 3-已处理")
    private Integer waitStatus;
    
    /**
     * 已等待天数
     */
    @Schema(description = "已等待天数")
    private Integer waitDays;
    
    /**
     * 满足的质押阈值
     */
    @Schema(description = "满足的质押阈值")
    private BigDecimal stakeThreshold;
    
    /**
     * 收货人姓名
     */
    @Schema(description = "收货人姓名")
    private String receiverName;
    
    /**
     * 收货人联系方式
     */
    @Schema(description = "收货人联系方式")
    private String receiverPhone;
    
    /**
     * 收货地址
     */
    @Schema(description = "收货地址")
    private String receiverAddress;

    public MyStakeDataVo(User user, StakeUser stakeUser, Asset asset) {
        if (user != null) {
            this.userId = user.getId();
            this.code = user.getCode();
            this.refCode = user.getRefCode();
            this.level = user.getLevel();
            this.teamCount = user.getTeamCount();
            this.realLevel = user.getRealLevel();
            this.istouch = user.getRealLevel() == this.level;
        }
        if (stakeUser != null) {
            this.todayStatic = stakeUser.getTodayStatic();
            this.totalStatic = stakeUser.getTotalStatic();
            this.todayDynamic = stakeUser.getTodayDynamic();
            this.totalDynamic = stakeUser.getTotalDynamic();
            this.pendingAmount = stakeUser.getPendingAmount();
            this.currentAmount = stakeUser.getCurrentAmount();
            this.staticPool = stakeUser.getStaticPool();
            this.dynamicPool = stakeUser.getDynamicPool();
            this.canReceive = stakeUser.getCanReceive();
            this.weekDynamic = stakeUser.getWeekDynamic();
            // 剩余额度
            this.limit = stakeUser.getLimitAmount();
            // 额度最小为0，可能是已经产出满额了，然后解除了质押就会出现负数
            if (limit.compareTo(BigDecimal.ZERO) < 0) {
                this.limit = BigDecimal.ZERO;
            }
            this.todayBuy = stakeUser.getTodayBuy();
            this.totalBuy = stakeUser.getTotalBuy();
        }
        this.balance = BigDecimal.ZERO;
        this.bnbBalance = BigDecimal.ZERO;
        
        this.tokenId = asset.getTokenId();
        this.totalStaked = asset.getTotalStaked();
        this.totalSupply = asset.getTotalSupply();
        this.totalBurn = asset.getTotalBurn();
        this.totalDao = asset.getTotalDao();
        // 返回70 %, 记录的是100%
        this.totalSupplyBnb = asset.getTotalSupplyBnb().multiply(new BigDecimal("0.7"));
        this.idoAddress = asset.getIdoAddress();
        this.stakeAddress = asset.getStakeAddress();
        this.tokenAddress = asset.getTokenAddress();
        
    }
}
