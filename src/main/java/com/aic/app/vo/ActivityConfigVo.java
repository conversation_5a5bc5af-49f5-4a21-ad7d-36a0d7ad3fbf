package com.aic.app.vo;

import com.aic.app.model.ActivityConfig;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 活动配置VO
 */
@Data
public class ActivityConfigVo {
    
    @Schema(description = "ID")
    private Long id;
    
    @Schema(description = "期数")
    private Integer period;
    
    @Schema(description = "该期固定质押数量")
    private BigDecimal stakeAmount;
    
    @Schema(description = "该期固定销毁数量")
    private BigDecimal burnAmount;
    
    @Schema(description = "活动开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    
    @Schema(description = "活动结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    
    @Schema(description = "活动状态 0-未开始 1-进行中 2-已结束")
    private Integer status;
    
    @Schema(description = "活动状态描述")
    private String statusDesc;
    
    @Schema(description = "参与人数限制，0表示无限制")
    private Integer participantLimit;
    
    @Schema(description = "当前参与人数")
    private Integer participantCount;
    
    @Schema(description = "已完成条件的人数")
    private Integer completedCount;
    
    @Schema(description = "是否已满员")
    private Boolean isFull;
    
    @Schema(description = "完成率(百分比)")
    private String completionRate;
    
    @Schema(description = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    @Schema(description = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    public ActivityConfigVo() {}
    
    public ActivityConfigVo(ActivityConfig config, Integer participantCount, Integer completedCount) {
        BeanUtils.copyProperties(config, this);
        
        this.participantCount = participantCount != null ? participantCount : 0;
        this.completedCount = completedCount != null ? completedCount : 0;
        
        // 设置状态描述
        this.statusDesc = getStatusDescription(config.getStatus());
        
        // 判断是否满员
        this.isFull = config.getParticipantLimit() != null && config.getParticipantLimit() > 0 
            && this.participantCount >= config.getParticipantLimit();
        
        // 计算完成率
        if (this.participantCount > 0) {
            double rate = (double) this.completedCount / this.participantCount * 100;
            this.completionRate = String.format("%.1f%%", rate);
        } else {
            this.completionRate = "0.0%";
        }
    }
    
    private String getStatusDescription(Integer status) {
        if (status == null) return "未知";
        return switch (status) {
            case 0 -> "未开始";
            case 1 -> "进行中";
            case 2 -> "已结束";
            default -> "未知";
        };
    }
} 