package com.aic.app.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 活动用户记录VO - 用于管理员查询
 */
@Data
public class ActivityUserRecordVo {
    
    @Schema(description = "ID")
    private Long id;
    
    @Schema(description = "用户ID")
    private String userId;
    
    @Schema(description = "邀请码")
    private String userCode;
    
    @Schema(description = "期数")
    private Integer period;
    
    @Schema(description = "质押状态 0-未质押 1-已质押")
    private Integer stakeStatus;
    
    @Schema(description = "质押状态描述")
    private String stakeStatusDesc;
    
    @Schema(description = "质押时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date stakeTime;
    
    @Schema(description = "质押天数")
    private Integer stakeDays;
    
    @Schema(description = "质押数量")
    private BigDecimal stakeAmount;
    
    @Schema(description = "销毁状态 0-未销毁 1-已销毁")
    private Integer burnStatus;
    
    @Schema(description = "销毁状态描述")
    private String burnStatusDesc;
    
    @Schema(description = "销毁时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date burnTime;
    
    @Schema(description = "销毁数量")
    private BigDecimal burnAmount;
    
    @Schema(description = "是否完成该期活动 0-未完成 1-已完成")
    private Integer completed;
    
    @Schema(description = "完成状态描述")
    private String completedDesc;
    
    @Schema(description = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    @Schema(description = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
