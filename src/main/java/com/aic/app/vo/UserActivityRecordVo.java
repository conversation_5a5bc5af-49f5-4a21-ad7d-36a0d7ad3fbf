package com.aic.app.vo;

import com.aic.app.model.UserActivityRecord;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户活动记录VO
 */
@Data
public class UserActivityRecordVo {
    
    @Schema(description = "ID")
    private Long id;
    
    @Schema(description = "用户ID")
    private String userId;
    
    @Schema(description = "用户编码")
    private String userCode;
    
    @Schema(description = "期数")
    private Integer period;
    
    @Schema(description = "质押状态 0-未质押 1-已质押")
    private Integer stakeStatus;
    
    @Schema(description = "质押状态描述")
    private String stakeStatusDesc;
    
    @Schema(description = "质押时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date stakeTime;
    
    @Schema(description = "质押数量")
    private BigDecimal stakeAmount;
    
    @Schema(description = "销毁状态 0-未销毁 1-已销毁")
    private Integer burnStatus;
    
    @Schema(description = "销毁状态描述")
    private String burnStatusDesc;
    
    @Schema(description = "销毁时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date burnTime;
    
    @Schema(description = "销毁数量")
    private BigDecimal burnAmount;
    
    @Schema(description = "是否完成该期活动 0-未完成 1-已完成")
    private Integer completed;

    @Schema(description = "完成状态描述")
    private String completedDesc;

    @Schema(description = "质押天数")
    private Integer stakeDays;

    @Schema(description = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    @Schema(description = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    public UserActivityRecordVo(UserActivityRecord record) {
        BeanUtils.copyProperties(record, this);
        this.stakeStatusDesc = record.getStakeStatus() == 1 ? "已质押" : "未质押";
        this.burnStatusDesc = record.getBurnStatus() == 1 ? "已销毁" : "未销毁";
        this.completedDesc = record.getCompleted() == 1 ? "已完成" : "未完成";
    }
    
    public UserActivityRecordVo() {
    }
}
