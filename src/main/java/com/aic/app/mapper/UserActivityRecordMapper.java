package com.aic.app.mapper;

import com.aic.app.model.UserActivityRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 用户活动记录Mapper
 */
@Mapper
public interface UserActivityRecordMapper extends BaseMapper<UserActivityRecord> {
    
    /**
     * 根据用户ID和期数查询记录
     */
    @Select("select * from user_activity_record where user_id = #{userId} and period = #{period}")
    UserActivityRecord getByUserIdAndPeriod(@Param("userId") String userId, @Param("period") Integer period);
    
    /**
     * 更新质押状态
     */
    @Update("update user_activity_record set stake_status = 1, stake_time = #{stakeTime}, stake_amount = #{stakeAmount}, " +
            "stake_days = 0, completed = case when stake_days >= 15 and burn_status = 1 then 1 else 0 end, update_time = now() " +
            "where user_id = #{userId} and period = #{period}")
    int updateStakeStatus(@Param("userId") String userId, @Param("period") Integer period,
                         @Param("stakeTime") Date stakeTime, @Param("stakeAmount") BigDecimal stakeAmount);
    
    /**
     * 更新解除质押状态
     */
    @Update("update user_activity_record set stake_status = 0, stake_time = null, " +
            "completed = 0, update_time = now() " +
            "where user_id = #{userId} and period = #{period}")
    int updateUnstakeStatus(@Param("userId") String userId, @Param("period") Integer period);
    
    /**
     * 更新销毁状态
     */
    @Update("update user_activity_record set burn_status = 1, burn_time = #{burnTime}, burn_amount = #{burnAmount}, " +
            "completed = case when stake_status = 1 then 1 else 0 end, update_time = now() " +
            "where user_id = #{userId} and period = #{period}")
    int updateBurnStatus(@Param("userId") String userId, @Param("period") Integer period, 
                        @Param("burnTime") Date burnTime, @Param("burnAmount") BigDecimal burnAmount);
    
    /**
     * 查询某期已完成的用户列表
     */
    @Select("select r.*, u.code from user_activity_record r " +
            "left join user u on r.user_id = u.id " +
            "where r.period = #{period} and r.completed = 1 " +
            "order by r.stake_time asc")
    List<UserActivityRecord> getCompletedUsersByPeriod(@Param("period") Integer period);
    
    /**
     * 查询某期已完成的用户列表（分页）
     */
    @Select("select r.*, u.code from user_activity_record r " +
            "left join user u on r.user_id = u.id " +
            "where r.period = #{period} and r.completed = 1 " +
            "order by r.stake_time asc")
    IPage<UserActivityRecord> getCompletedUsersByPeriod(@Param("period") Integer period, Page<UserActivityRecord> page);
    
    /**
     * 查询用户所有活动记录
     */
    @Select("select * from user_activity_record where user_id = #{userId} order by period desc")
    List<UserActivityRecord> getUserActivityRecords(@Param("userId") String userId);
    
    /**
     * 计算用户当前活动质押总量
     */
    @Select("select ifnull(sum(stake_amount), 0) from user_activity_record " +
            "where user_id = #{userId} and stake_status = 1")
    BigDecimal getUserActivityStakeAmount(@Param("userId") String userId);
    
    /**
     * 统计某期活动的参与人数
     */
    @Select("select count(distinct user_id) from user_activity_record where period = #{period}")
    int getParticipantCount(@Param("period") Integer period);
    
    /**
     * 统计某期活动达成条件的人数
     */
    @Select("select count(*) from user_activity_record where period = #{period} and completed = 1")
    int getCompletedCount(@Param("period") Integer period);
}
