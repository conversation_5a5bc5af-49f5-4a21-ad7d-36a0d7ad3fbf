package com.aic.app.mapper;

import com.aic.app.model.UserModel;
import com.aic.app.model.UserProduct;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface UserProductMapper extends BaseMapper<UserProduct> {
    
    @Select("select user_id as id, SUM(IF(product_id=1,power,0)) as product_current_amount,  SUM(IF(product_id>1,power,0)) as product_regular_amount, SUM(IF(product_id=2,power,0)) as product_amount30, SUM(IF(product_id in (3,7),power,0)) as product_amount180, SUM(IF(product_id in (3),power,0)) as product_amount180_b, SUM(IF(product_id in (7),power,0)) as product_amount180_u, SUM(IF(product_id in (4,5,6),power,0)) as product_amount360, SUM(IF(product_id in (4,6),power,0)) as product_amount360_b, SUM(IF(product_id in (5),power,0)) as product_amount360_u " +
            ", SUM(IF(product_id in (3,7) and pay_method='DOP',amount1,0)) as amount180_dop, SUM(IF(product_id in (3,7) and pay_method='DOP+FISH',amount1,0)) as amount180_dop_fish,  SUM(IF(product_id in (3,7) and pay_method='DOP+FISH',amount2,0)) as amount180_fish, SUM(IF(product_id in (3,7) and pay_method='DOP+BIRD',amount1,0)) as amount180_dop_bird, SUM(IF(product_id in (3,7) and pay_method='DOP+BIRD',amount2,0)) as amount180_bird, SUM(IF(product_id in (3,7) and pay_method='DOP+NAC',amount1,0)) as amount180_dop_nac, SUM(IF(product_id in (3,7) and pay_method='DOP+NAC',amount2,0)) as amount180_nac, SUM(IF(product_id in (3,7) and pay_method='DOP+FOMO',amount1,0)) as amount180_dop_fomo, SUM(IF(product_id in (3,7) and pay_method='DOP+FOMO',amount2,0)) as amount180_fomo " + 
            ", SUM(IF(product_id in (4,5,6) and pay_method='DOP',amount1,0)) as amount360_dop, SUM(IF(product_id in (4,5,6) and pay_method='DOP+FISH',amount1,0)) as amount360_dop_fish, SUM(IF(product_id in (4,5,6) and pay_method='DOP+FISH',amount2,0)) as amount360_fish, SUM(IF(product_id in (4,5,6) and pay_method='DOP+BIRD',amount1,0)) as amount360_dop_bird, SUM(IF(product_id in (4,5,6) and pay_method='DOP+BIRD',amount2,0)) as amount360_bird, SUM(IF(product_id in (4,5,6) and pay_method='DOP+NAC',amount1,0)) as amount360_dop_nac, SUM(IF(product_id in (4,5,6) and pay_method='DOP+NAC',amount2,0)) as amount360_nac, SUM(IF(product_id in (4,5,6) and pay_method='DOP+FOMO',amount1,0)) as amount360_dop_fomo, SUM(IF(product_id in (4,5,6) and pay_method='DOP+FOMO',amount2,0)) as amount360_fomo " +
            "from user_product ${ew.customSqlSegment}")
    List<UserModel> findAllGroupByProduct(@Param(Constants.WRAPPER) Wrapper<UserProduct> qw);

    @Select("select 1" +
            ", SUM(IF(product_id in (3,7) and pay_method='DOP',amount1,0)) as amount180_dop, SUM(IF(product_id in (3,7) and pay_method='DOP+FISH',amount1,0)) as amount180_dop_fish,  SUM(IF(product_id in (3,7) and pay_method='DOP+FISH',amount2,0)) as amount180_fish, SUM(IF(product_id in (3,7) and pay_method='DOP+BIRD',amount1,0)) as amount180_dop_bird, SUM(IF(product_id in (3,7) and pay_method='DOP+BIRD',amount2,0)) as amount180_bird, SUM(IF(product_id in (3,7) and pay_method='DOP+NAC',amount1,0)) as amount180_dop_nac, SUM(IF(product_id in (3,7) and pay_method='DOP+NAC',amount2,0)) as amount180_nac, SUM(IF(product_id in (3,7) and pay_method='DOP+FOMO',amount1,0)) as amount180_dop_fomo, SUM(IF(product_id in (3,7) and pay_method='DOP+FOMO',amount2,0)) as amount180_fomo " +
            ", SUM(IF(product_id in (4,5,6) and pay_method='DOP',amount1,0)) as amount360_dop, SUM(IF(product_id in (4,5,6) and pay_method='DOP+FISH',amount1,0)) as amount360_dop_fish, SUM(IF(product_id in (4,5,6) and pay_method='DOP+FISH',amount2,0)) as amount360_fish, SUM(IF(product_id in (4,5,6) and pay_method='DOP+BIRD',amount1,0)) as amount360_dop_bird, SUM(IF(product_id in (4,5,6) and pay_method='DOP+BIRD',amount2,0)) as amount360_bird, SUM(IF(product_id in (4,5,6) and pay_method='DOP+NAC',amount1,0)) as amount360_dop_nac, SUM(IF(product_id in (4,5,6) and pay_method='DOP+NAC',amount2,0)) as amount360_nac, SUM(IF(product_id in (4,5,6) and pay_method='DOP+FOMO',amount1,0)) as amount360_dop_fomo, SUM(IF(product_id in (4,5,6) and pay_method='DOP+FOMO',amount2,0)) as amount360_fomo " +
            " from user_product ${ew.customSqlSegment}")
    UserModel sumProductAmount(@Param(Constants.WRAPPER) Wrapper<UserProduct> qw);

    @Select("select 1" +
            ", SUM(IF(product_id in (3,4,5,6,7) and pay_method='DOP',amount1,0)) as amount180_dop, SUM(IF(product_id in (3,4,5,6,7) and pay_method='DOP+FISH',amount1,0)) as amount180_dop_fish,  SUM(IF(product_id in (3,4,5,6,7) and pay_method='DOP+FISH',amount2,0)) as amount180_fish, SUM(IF(product_id in (3,4,5,6,7) and pay_method='DOP+BIRD',amount1,0)) as amount180_dop_bird, SUM(IF(product_id in (3,4,5,6,7) and pay_method='DOP+BIRD',amount2,0)) as amount180_bird, SUM(IF(product_id in (3,4,5,6,7) and pay_method='DOP+NAC',amount1,0)) as amount180_dop_nac, SUM(IF(product_id in (3,4,5,6,7) and pay_method='DOP+NAC',amount2,0)) as amount180_nac, SUM(IF(product_id in (3,4,5,6,7) and pay_method='DOP+FOMO',amount1,0)) as amount180_dop_fomo, SUM(IF(product_id in (3,4,5,6,7) and pay_method='DOP+FOMO',amount2,0)) as amount180_fomo   " +
            " from user_product ${ew.customSqlSegment}")
    UserModel sumProductAmount2(@Param(Constants.WRAPPER) Wrapper<UserProduct> qw);

    @Select("""
        <script>
            select 
                a.*,c.code
            from
                user_product a 
                left join (select id,code from user) c on a.user_id = c.id
            <if test="ew.customSqlSegment != '' and ew.customSqlSegment != null">
                ${ew.customSqlSegment}
            </if>
        </script>
    """)
    <E extends IPage<UserProduct>> E page(E page, @Param(Constants.WRAPPER) Wrapper<UserProduct> queryWrapper);

    @Update("update user_product set rate=0.004 where product_id=6 and rate != 0.004")
    int updateProduct6Rate1();

    @Update("update user_product set rate=0.008 where product_id=6 and rate != 0.008 and user_id in (select user_id from reward_log where level=-1 and symbol='DOP' group by user_id having sum(product_amount) >= 1000)")
    int updateProduct6Rate2();

    /**
     * 统计伞下用户锁仓相关收益数据（通过user_log表）
     * 静态收益类型：2、3
     * 动态收益：4
     * 直推：10
     * 每天释放：41
     */
    @Select("select 1" +
            ", IFNULL(SUM(IF(ul.type in (2,3), ul.amount, 0)), 0) as stake_static_amount" +
            ", IFNULL(SUM(IF(ul.type=4, ul.amount, 0)), 0) as stake_dynamic_amount" +
            ", IFNULL(SUM(IF(ul.type=10, ul.amount, 0)), 0) as stake_direct_amount" +
            ", IFNULL(SUM(IF(ul.type=41, ul.amount, 0)), 0) as stake_release_amount" +
            " from dop.user_log ul ${ew.customSqlSegment}")
    UserModel sumStakeLogAmount(@Param(Constants.WRAPPER) Wrapper<Object> qw);
}
