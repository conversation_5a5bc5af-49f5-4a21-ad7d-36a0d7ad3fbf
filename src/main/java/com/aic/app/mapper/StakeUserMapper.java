package com.aic.app.mapper;

import com.aic.app.model.*;
import com.aic.app.vo.RankVo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.*;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface StakeUserMapper extends BaseMapper<StakeUser> {
    
    @Update("update user set limit_amount = limit_amount - #{limitAmount}, pid = #{pid} where id = #{id} and limit_amount - #{limitAmount} >= 0")
    int updateUserAmount(@Param("id") Long id, @Param("limitAmount") BigDecimal limitAmount, @Param("pid") String pid);
    
    @Update("update stake_user set pending_amount = pending_amount + #{pendingAmount} where id = #{id}")
    int updateUserPendingAmount(@Param("id") Long id, @Param("pendingAmount") BigDecimal pendingAmount);
    
    @Update("update stake_user set current_amount = current_amount + #{amount} where id = #{id} and current_amount + #{amount} >= 0")
    int updateUserCurrentAmount(@Param("id") Long id, @Param("amount") BigDecimal amount);
    
    @Select("""
        <script>
            select 
                a.*,b.layer,c.code as ref_code
            from
                user a 
                left join user_relation b on a.id=b.id
                left join user c on a.pid = c.id
            <if test="ew.customSqlSegment != '' and ew.customSqlSegment != null">
                ${ew.customSqlSegment}
            </if>
        </script>
    """)
    IPage<UserModel> findAllForAdminUser(Page<User> page, @Param(Constants.WRAPPER) QueryWrapper<User> queryWrapper);

    @Select("""
        <script>
            select 
                <choose>
                    <when test="ew.sqlSelect != '' and ew.sqlSelect != null">
                        ${ew.sqlSelect}
                    </when>
                    <otherwise>
                        c.*,b.layer,a.code,(c.current_amount+c.pending_amount) as total_amount,a.pid,d.code as ref_code
                    </otherwise>
                </choose>
            from
                user a
                inner join stake_user c on a.id = c.user_id
                left join user_relation b on a.id=b.id
                 left join user d on a.pid = d.id
            <if test="ew.customSqlSegment != '' and ew.customSqlSegment != null">
                ${ew.customSqlSegment}
            </if>
        </script>
    """)
    IPage<StakeUserModel> findAllForAdmin(Page<StakeUser> page, @Param(Constants.WRAPPER) QueryWrapper<StakeUser> queryWrapper);

    @Insert("insert into stake_user(user_id,project_id) select a.id, #{projectId} from user a left join stake_user b on b.user_id = a.id and b.project_id = #{projectId} where b.id is null")
    int syncUsers(@Param("projectId") Long projectId);

    @Update("update stake_user set static_pool = static_pool - #{amount} where id = #{id} and static_pool - #{amount} >= 0")
    int updateStaticPool(@Param("id") Long id, @Param("amount") BigDecimal amount);

    @Update("update stake_user set dynamic_pool = dynamic_pool - #{amount} where id = #{id} and dynamic_pool - #{amount} >= 0")
    int updateDynamicPool(@Param("id") Long id, @Param("amount") BigDecimal amount);

    @Select("SELECT a.id,a.user_id as uid,b.code,GREATEST(a.team_perf - a.max_team_perf, 0) as team_perf FROM stake_user a, user b WHERE a.user_id = b.id and ((a.team_perf - a.max_team_perf) > 0 \n" +
            " AND a.token_id = #{tokenId}) ORDER BY GREATEST(a.team_perf - a.max_team_perf, 0) DESC limit #{size}")
    List<RankVo> listTeamPerfTop(@Param("tokenId") String tokenId, @Param("size") int size);

    @Select("select a.user_id,b.pid,a.token_id, (a.current_amount+a.pending_amount) amount, a.max_team_perf,a.max_node_perf,a.team_perf,a.node_perf,a.node,a.sum_amount from stake_user a, user b where a.user_id=b.id and a.token_id=#{tokenId}")
    List<StakeModel> listStakeUsers(@Param("tokenId") String tokenId);

    @Update("update stake_user set stake_first = 1, stake_limit = #{amount}, burn_limit = burn_limit + #{amount} where id = #{id} and stake_first=0")
    int updateUserLimitAmount(@Param("id") Long id, @Param("amount") BigDecimal amount);

    @Update("update stake_user set stake_limit = GREATEST(stake_limit - #{amount}, 0), burn_limit = GREATEST(burn_limit - #{amount}, 0) where id = #{id}")
    int updateUserLimitAmount2(@Param("id") Long id, @Param("amount") BigDecimal amount);

    @Select("select a.id, a.user_id, a.node, a.token_id, b.pid, b.level from stake_user a, user b where a.user_id = b.id and a.user_id = #{userId} and a.token_id = #{tokenId}")
    StakeUser getStakeUserLevelPid(@Param("userId") String userId, @Param("tokenId") String tokenId);

    @Select("""
            select user_id, sum(IF(type=0,quantity,-quantity)) as total, 
                sum(IF(DATE_FORMAT(create_time, '%Y-%m-%d')=DATE_FORMAT(now(), '%Y-%m-%d'), IF(type=0,quantity,-quantity), 0)) today
                from user_stake GROUP BY user_id
            """)
    List<UserStakeModel> sumStakes();

    @Select("select a.*, b.pid from stake_user a, user b where a.user_id = b.id and a.token_id = #{tokenId}")
    List<StakeUser> list2(@Param("tokenId") String tokenId);

    @Update("update stake_user set wait_status = #{status}, wait_days = #{days}, stake_threshold = #{threshold} where id = #{id}")
    int updateWaitStatus(@Param("id") Long id, @Param("status") Integer status, @Param("days") Integer days, @Param("threshold") BigDecimal threshold);

    @Update("update stake_user set wait_status = 0, wait_days = 0, stake_threshold = null where id = #{id}")
    int resetWaitStatus(@Param("id") Long id);

    @Update("update stake_user set stake_limit = GREATEST(stake_limit - #{amount}, 0), burn_limit = GREATEST(burn_limit - #{amount}, 0), today_unstake_deduction = COALESCE(today_unstake_deduction, 0) + #{amount} where id = #{id}")
    int updateUserLimitAmountWithDeduction(@Param("id") Long id, @Param("amount") BigDecimal amount);

    @Update("update stake_user set stake_limit = stake_limit + #{amount}, burn_limit = burn_limit + #{amount}, today_unstake_deduction = GREATEST(COALESCE(today_unstake_deduction, 0) - #{amount}, 0) where id = #{id} and COALESCE(today_unstake_deduction, 0) >= #{amount}")
    int recoverUserLimitAmount(@Param("id") Long id, @Param("amount") BigDecimal amount);

    @Update("update stake_user set today_unstake_deduction = 0")
    int resetTodayUnstakeDeduction();
}
